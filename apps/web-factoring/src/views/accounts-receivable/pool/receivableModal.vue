<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import { ref, watch } from 'vue';

import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { message, Modal } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getReceivablePageListApi } from '#/api';

const props = defineProps({
  isVisible: {
    type: Boolean,
    required: true,
  },
  poolAssetsMin: {
    type: Number,
    default: null,
  },
  poolAssetsMax: {
    type: Number,
    default: null,
  },
  poolAssetsValidMin: {
    type: Number,
    default: null,
  },
  poolAssetsValidMax: {
    type: Number,
    default: null,
  },
});

const emit = defineEmits(['update:isVisible', 'confirm']);
// 定义模态框显示状态
const visible = ref(false);

const handleCancel = () => {
  visible.value = false;
  emit('update:isVisible', false);
};

const dictStore = useDictStore();

// 表单配置
const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'receivableName',
      label: '应收账款名称',
    },
    {
      component: 'Input',
      fieldName: 'creditorName',
      label: '债权人',
    },
    {
      component: 'Input',
      fieldName: 'debtorName',
      label: '债务人',
    },
    {
      component: 'Select',
      fieldName: 'bizTypeList',
      label: '业务类型',
      componentProps: {
        options: dictStore.getDictList('FCT_FACTORING_TYPE'),
      },
    },
  ],
  showCollapseButton: false,
  submitOnEnter: true,
});

// 表格配置
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: 60 }, // 添加单选框列
    { type: 'seq', title: '序号', width: 60 },
    { field: 'receivableName', title: '应收账款名称', width: 280 },
    { field: 'projectName', title: '关联项目', width: 280 },
    { field: 'creditorName', title: '债权人' },
    { field: 'debtorName', title: '债务人' },
    {
      field: 'bizType',
      title: '业务类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_FACTORING_TYPE',
        },
      },
    },
    { field: 'receivableAmount', title: '应收账款金额（元）' },
    { field: 'receivableDueDate', title: '应收账款到期日', formatter: 'formatDate' },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_NOT_REVIEW_STATUS',
        },
      },
    },
    {
      field: 'zdStatus',
      title: '中登网登记状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'RECEIVABLE_ZD_STATUS',
        },
      },
    },
  ],
  // height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getReceivablePageListApi({
          poolAssetsMin: props.poolAssetsMin,
          poolAssetsMax: props.poolAssetsMax,
          poolAssetsValidMin: props.poolAssetsValidMin,
          poolAssetsValidMax: props.poolAssetsValidMax,
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [Grid, gridApi] = useVbenVxeGrid({ formOptions, gridOptions });

// 确认方法
const confirmFinanceCompare = async () => {
  const $grid = gridApi.grid;
  const rowList = $grid.getCheckboxRecords();
  // 检查是否有选中的行
  if (!rowList) {
    message.warning('请最少选择一条数据');
    return;
  }
  emit('confirm', rowList);
  // 确认后关闭模态框
  visible.value = false;
  emit('update:isVisible', false);
};

watch(
  () => props.isVisible,
  (newVal) => {
    visible.value = newVal;
    if (newVal && Object.keys(gridApi.grid).length > 0) {
      gridApi.grid.commitProxy('query');
    }
  },
);
</script>

<template>
  <Modal :open="visible" title="选择应收账款" width="80%" @cancel="handleCancel" @ok="confirmFinanceCompare">
    <Grid />
  </Modal>
</template>

<style scoped></style>
