<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { RepaymentRecordInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { defineFormOptions } from '@vben/utils';

import { Modal as AntdModal, message } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { delRepaymentRecordApi, getRepaymentRecordPageListApi } from '#/api';

import RepaymentRecordDetail from './payment-record-detail.vue';
import RepaymentRecordEdit from './payment-record-edit.vue';

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'repaymentConfirmCode',
      label: '还款记录编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '还款企业',
    },
    {
      component: 'RangePicker',
      fieldName: 'repaymentDate',
      label: '还款日期',
    },
  ],
  fieldMappingTime: [
    ['repaymentDate', ['beginRepaymentDate', 'endRepaymentDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'repaymentConfirmCode', title: '还款记录编号', minWidth: 180 },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    {
      field: 'projectType',
      title: '项目类型',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PROJECT_TYPE',
        },
      },
    },
    { field: 'creditApplyName', title: '关联用信申请', minWidth: 200 },
    { field: 'paymentApplyCode', title: '关联付款申请编号', minWidth: 180 },
    { field: 'paymentConfirmCode', title: '付款记录编号', minWidth: 200 },
    { field: 'repaymentPlanCode', title: '还款计划编号', minWidth: 180 },
    { field: 'companyName', title: '还款企业', minWidth: 180 },
    { field: 'repaymentAmount', title: '还款金额(元)', minWidth: 120 },
    { field: 'repaymentDate', title: '还款日期', minWidth: 120, formatter: 'formatDate' },
    {
      field: 'status',
      title: '操作状态',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_STATUS',
        },
      },
    },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 140,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getRepaymentRecordPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const add = () => {
  openFormPopup(true, {});
};
const edit = (row: RepaymentRecordInfo) => {
  openFormPopup(true, row);
};
const editSuccess = () => {
  gridApi.reload();
};
const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: RepaymentRecordInfo) => {
  openDetailPopup(true, row);
};
const del = (row: RepaymentRecordInfo) => {
  const id = row.id;
  AntdModal.confirm({
    title: '确认删除',
    content: '此操作将删除该还款记录，是否继续？',
    async onOk() {
      await delRepaymentRecordApi(id);
      message.success($t('base.resSuccess'));
      await gridApi.reload();
    },
  });
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-space>
          <a-button class="mr-2" type="primary" @click="add">
            {{ $t('base.add') }}
          </a-button>
        </a-space>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link v-if="['SUBMIT'].includes(row.status)" @click="edit(row)">
            {{ $t('base.edit') }}
          </a-typography-link>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
          <a-typography-link type="danger" v-if="['SUBMIT'].includes(row.status)" @click="del(row)">
            {{ $t('base.del') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <RepaymentRecordEdit @register="registerForm" @ok="editSuccess" />
    <RepaymentRecordDetail @register="registerDetail" />
  </Page>
</template>

<style></style>
