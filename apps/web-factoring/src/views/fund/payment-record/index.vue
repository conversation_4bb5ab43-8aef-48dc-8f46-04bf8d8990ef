<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { PaymentRecordInfo } from '#/api';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getPaymentRecordPageListApi } from '#/api';

import PaymentRecordDetail from './payment-record-detail.vue';

const dictStore = useDictStore();

const formOptions = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Input',
      fieldName: 'payeeCompanyName',
      label: '收款单位',
    },
    {
      component: 'Select',
      fieldName: 'confirmPaymentMethod',
      label: '付款方式',
      componentProps: {
        options: dictStore.getDictList('FCT_PAYMENT_METHOD'),
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'launchDate',
      label: '投放日期',
    },
  ],
  fieldMappingTime: [
    ['launchDate', ['beginConfirmInvestDate', 'endConfirmInvestDate'], ['YYYY-MM-DD 00:00:00', 'YYYY-MM-DD 23:59:59']],
  ],
  commonConfig: {
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'confirmCode', title: '付款记录编号', minWidth: 200 },
    { field: 'projectName', title: '项目名称', minWidth: 200 },
    { field: 'confirmInvestAmount', title: '投放金额（元）' },
    { field: 'confirmInvestDate', title: '投放日期', formatter: 'formatDate' },
    {
      field: 'confirmPaymentMethod',
      title: '付款方式',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'FCT_PAYMENT_METHOD',
        },
      },
    },
    { field: 'payerCompanyName', title: '付款单位' },
    { field: 'payeeCompanyName', title: '收款单位' },
    { field: 'payeeBankBranch', title: '收款单位开户行' },
    { field: 'payeeBankAccount', title: '收款单位银行账号' },
    { field: 'remarks', title: '备注' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 120,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getPaymentRecordPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar-tools',
    },
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [registerDetail, { openPopup: openDetailPopup }] = usePopup();
const viewDetail = (row: PaymentRecordInfo) => {
  openDetailPopup(true, row);
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="viewDetail(row)">
            {{ $t('base.detail') }}
          </a-typography-link>
        </a-space>
      </template>
    </Grid>
    <PaymentRecordDetail @register="registerDetail" />
  </Page>
</template>

<style></style>
