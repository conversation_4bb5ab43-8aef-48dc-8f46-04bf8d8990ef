<script setup lang="ts">
import type { BasicAssetInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { getBasicAssetInfoApi } from '#/api';
import AccountsReceivable from '#/views/asset-securities/basic-asset/components/accounts-receivable.vue';

defineEmits(['register']);

// 详情页样式配置
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};

// 初始化数据
const init = async (data: BasicAssetInfo) => {
  // 获取详情数据，如果有ID则从接口获取，否则使用传入的数据
  const info = data.id ? await getBasicAssetInfoApi({ id: data.id }) : data;
  basicForm.value = { ...basicForm.value, ...info };
};

const basicForm = ref<BasicAssetInfo>({});
const [registerPopup] = usePopupInner(init);
</script>

<template>
  <BasicPopup title="ABS基础资产信息" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-descriptions v-bind="descriptionsProp" class="mt-4">
        <a-descriptions-item label="关联融资项目">
          {{ basicForm.projectName }}
        </a-descriptions-item>
        <a-descriptions-item label="关联用信申请" v-if="basicForm.projectType === 'comprehensive'">
          {{ basicForm.creditApplyName }}
        </a-descriptions-item>
        <a-descriptions-item label="ABS基础资产编号">
          {{ basicForm.absAssetCode }}
        </a-descriptions-item>

        <a-descriptions-item label="保理合同编号">
          {{ basicForm.factoringContractCode }}
        </a-descriptions-item>

        <a-descriptions-item label="ABS基础资产名称">
          {{ basicForm.absAssetName }}
        </a-descriptions-item>

        <a-descriptions-item label="保理融资金额（元）">
          {{ basicForm.financingAmount }}
        </a-descriptions-item>

        <a-descriptions-item label="应收账款总金额（元）">
          {{ basicForm.receivableAmount }}
        </a-descriptions-item>

        <a-descriptions-item label="融资利率（%）">
          {{ basicForm.financingRate }}
        </a-descriptions-item>
      </a-descriptions>

      <AccountsReceivable :receivable-form="basicForm" />
    </div>
  </BasicPopup>
</template>

<style></style>
