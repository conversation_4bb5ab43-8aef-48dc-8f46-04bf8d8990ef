<script setup lang="ts">
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { InitiationInfo } from '#/api';

import { nextTick, watch } from 'vue';

import { DETAIL_GRID_OPTIONS } from '@vben/constants';
import { BasicCaption } from '@vben/fe-ui';

import { useVbenVxeGrid } from '#/adapter/vxe-table';

const props = defineProps({
  info: { type: Object, default: () => ({}) },
});
const GridOptions = {
  columns: [
    { field: 'receivableName', title: '应收账款名称', width: 180 },
    { field: 'creditorName', title: '债权人' },
    { field: 'debtorName', title: '债务人' },
    { field: 'receivableAmount', title: '应收账款金额（元）' },
    { field: 'receivableTerm', title: '应收账款期限（月）' },
    { field: 'receivableDueDate', title: '应收账款到期日', formatter: 'formatDate' },
  ],
  ...DETAIL_GRID_OPTIONS,
} as VxeTableGridOptions;
const [Grid, gridApi] = useVbenVxeGrid({
  gridOptions: GridOptions,
  gridEvents: {
    checkboxChange: ({ checked, row }: { checked: boolean; row: InitiationInfo }) => {
      if (checked) {
        gridApi.grid.setAllCheckboxRow(false);
        gridApi.grid.setCheckboxRow(row, true);
      }
    },
  },
});
watch(
  () => props.info,
  (val = {}) => {
    nextTick(() => {
      gridApi.grid.reloadData(val.receivableRefList ?? []);
    });
  },
  { immediate: true, deep: true },
);
</script>

<template>
  <div>
    <BasicCaption content="应收账款信息" />
    <Grid />
  </div>
</template>
