<script setup lang="ts">
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';

import LadderPenaltyDetail from '#/views/project/components/ladder-penalty-detail.vue';

defineProps({
  debtServiceForm: { type: Object, default: () => ({}) },
  descriptionsProp: { type: Object, default: () => ({}) },
  debtType: { type: String, default: () => '' },
});
const dictStore = useDictStore();
</script>

<template>
  <div>
    <BasicCaption content="还本付息方案" />
    <a-descriptions v-bind="descriptionsProp" class="mt-4">
      <a-descriptions-item label="宽限期天数（天）">
        {{ debtServiceForm.gracePeriodDays }}
      </a-descriptions-item>
      <a-descriptions-item label="宽限期费率（%/年）">
        {{ debtServiceForm.gracePeriodRate }}
      </a-descriptions-item>
      <a-descriptions-item label="服务费（元）">
        {{ debtServiceForm.serviceFeeAmount }}
      </a-descriptions-item>
      <a-descriptions-item label="融资比例（%）" v-if="debtType !== 'creditPricing'">
        {{ debtServiceForm.financingRatio }}
      </a-descriptions-item>
      <a-descriptions-item label="罚息类型">
        {{ dictStore.formatter(debtServiceForm.penaltyType, 'FCT_PENALTY_TYPE') }}
      </a-descriptions-item>
      <a-descriptions-item v-if="debtServiceForm.penaltyType === 'fixed'" label="固定罚息利率（%）">
        {{ debtServiceForm.penaltyInterestRate }}
      </a-descriptions-item>
    </a-descriptions>
    <LadderPenaltyDetail v-show="debtServiceForm.penaltyType === 'ladder'" :penalty-form="debtServiceForm" />
  </div>
</template>

<style></style>
