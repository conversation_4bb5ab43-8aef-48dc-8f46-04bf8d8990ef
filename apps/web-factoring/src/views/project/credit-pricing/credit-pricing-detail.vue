<script setup lang="ts">
import type { CreditPricingInfo } from '#/api';

import { ref } from 'vue';

import { BASE_PAGE_CLASS_NAME, DESCRIPTIONS_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { omit } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getCreditPricingInfoApi } from '#/api';
import DebtServiceDetail from '#/views/project/components/debt-service-detail.vue';
import RepaymentCalculationDetail from '#/views/project/components/repayment-calculation-detail.vue';
import RepaymentCalculationHistory from '#/views/project/components/repayment-calculation-history.vue';
import BaseDetail from '#/views/project/credit-pricing/components/base-detail.vue';
import PricingScheme from '#/views/project/credit-pricing/components/pricing-scheme.vue';

defineEmits(['register']);
const descriptionsProp = {
  ...DESCRIPTIONS_PROP,
  labelStyle: {
    width: '220px',
    justifyContent: 'flex-end',
  },
};
const init = async (data: CreditPricingInfo) => {
  let info = data.id ? await getCreditPricingInfoApi(data.id as number) : data;
  const calculation = omit(
    info.calculation,
    'id',
    'targetCompanyName',
    'targetCompanyCode',
    'projectCode',
    'projectName',
  );
  info = {
    ...info,
    ...calculation,
  };
  creditPricingForm.value = info;
  RepaymentCalculationHistoryRef.value.init(creditPricingForm.value);
};
const [registerPopup] = usePopupInner(init);
const RepaymentCalculationHistoryRef = ref();
const creditPricingForm = ref<CreditPricingInfo>({});
</script>

<template>
  <BasicPopup title="用信定价" @register="registerPopup">
    <div :class="BASE_PAGE_CLASS_NAME">
      <BaseDetail :credit-pricing-form="creditPricingForm" :descriptions-prop="descriptionsProp" />
      <PricingScheme :credit-pricing-form="creditPricingForm" :descriptions-prop="descriptionsProp" />
      <DebtServiceDetail
        :debt-service-form="creditPricingForm"
        :descriptions-prop="descriptionsProp"
        debt-type="creditPricing"
      />
      <RepaymentCalculationDetail
        :calculation-form="creditPricingForm"
        :descriptions-prop="descriptionsProp"
        calculation-type="CreditPricing"
      />
      <RepaymentCalculationHistory ref="RepaymentCalculationHistoryRef" calculation-type="CreditPricing" />
      <BaseAttachmentList :business-id="creditPricingForm.id" business-type="FCT_PROJECT_CREDIT_PRICING" />
    </div>
  </BasicPopup>
</template>

<style></style>
