<script setup lang="ts">
import type { CreditPricingInfo, InitiationInfo } from '#/api';

import { reactive, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP, FORM_PROP, FULL_FORM_ITEM_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { useDictStore } from '@vben/stores';

import { message, Select } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import {
  addCreditPricingApi,
  editCreditPricingApi,
  getCompanyLimitDetailApi,
  getCompanyListApi,
  getCompanyRatingListApi,
  getCreditPricingInfoApi,
  getProjectLimitApi,
  getProjectListApi,
} from '#/api';
import LadderPenalty from '#/views/project/components/ladder-penalty.vue';
import RepaymentCalculation from '#/views/project/components/repayment-calculation.vue';

const emit = defineEmits(['ok', 'register']);

const dictStore = useDictStore();
const RepaymentCalculationRef = ref();
const LadderPenaltyRef = ref();
const init = async (data: CreditPricingInfo) => {
  creditPricingForm.value = {};
  if (data.id) {
    let info = data.id ? await getCreditPricingInfoApi(data.id as number) : data;
    const { id: _, ...calculation } = info.calculation;
    info = {
      ...info,
      ...calculation,
    };
    info.expectedLaunchDate = dayjs(info.expectedLaunchDate).valueOf().toString();
    info.expectedDueDate = dayjs(info.expectedDueDate).valueOf().toString();
    creditPricingForm.value = info;
    LadderPenaltyRef.value.init(creditPricingForm.value);
    RepaymentCalculationRef.value.init(creditPricingForm.value);
  }
};
const [registerPopup, { closePopup }] = usePopupInner(init);
const FormRef = ref();
const creditPricingForm = ref<CreditPricingInfo>({});
const loading = reactive({
  submit: false,
});
const save = async (type: string) => {
  const repaymentResult = await RepaymentCalculationRef.value.save();
  if (!repaymentResult) {
    return;
  }
  await LadderPenaltyRef.value.save();
  await FormRef.value.validate();
  let api = addCreditPricingApi;
  if (creditPricingForm.value.id) {
    api = editCreditPricingApi;
  }
  const formData = cloneDeep(creditPricingForm.value);
  formData.expectedLaunchDate = Number(formData.expectedLaunchDate);
  formData.expectedDueDate = Number(formData.expectedDueDate);
  formData.calculation = { ...formData };
  delete formData.detailList;
  loading.submit = true;
  if (type === 'submit') formData.isSubmit = true;
  try {
    const res = await api(formData);
    message.success('保存成功');
    closePopup();
    emit('ok', res);
  } finally {
    loading.submit = false;
  }
};

const selectInitiation = async (_value: number, data: InitiationInfo) => {
  creditPricingForm.value.pricingName = `${data.label}-定价`;
  creditPricingForm.value.expectedUseAmount = data.receivableAmount;
  creditPricingForm.value.projectName = data.label;
  const res = await getProjectLimitApi({ projectId: data.projectId });
  const {
    creditAmount: pricingCreditAmount,
    creditTerm: pricingCreditTerm,
    creditRate: pricingCreditRate,
    creditType: pricingCreditType,
  } = res;
  const { projectType, creditCalculateAmount, targetCompanyCode } = data;
  creditPricingForm.value = {
    ...creditPricingForm.value,
    projectType,
    targetCompanyCode,
    creditCalculateAmount,
    pricingCreditAmount,
    pricingCreditTerm,
    pricingCreditRate,
    pricingCreditType,
  };
  creditPricingForm.value.customerLevel = await getCompanyRatingListApi({
    companyCode: creditPricingForm.value.targetCompanyCode,
  });
  const company = await getCompanyLimitDetailApi(creditPricingForm.value.targetCompanyCode);
  creditPricingForm.value.customerCategory = company.companyType;
};
const selectCompany = (_value: number, data: any, type: string) => {
  creditPricingForm.value[type] = data.label;
};
const rules = {
  projectId: [{ required: true, message: '请选择关联项目', trigger: 'change' }],
  pricingName: [{ required: true, message: '请输入定价名称' }],
  creditCompanyCode: [{ required: true, message: '请选择授信企业', trigger: 'change' }],
  creditUseCompanyCode: [{ required: true, message: '请选择用信主体', trigger: 'change' }],
  pricingBasicRatio: [{ required: true, message: '请输入基准利率' }],
  pricingXirrRate: [{ required: true, message: '请输入综合收益率' }],
  serviceFeeAmount: [{ required: true, message: '请输入服务费' }],
  gracePeriodDays: [{ required: true, message: '请输入宽限期天数' }],
  gracePeriodRate: [{ required: true, message: '请输入宽限期费率' }],
  penaltyInterestRate: [{ required: true, message: '请输入固定罚息利率' }],
  planningMethod: [{ required: true, message: '请选择还本付息计划规划方式', trigger: 'change' }],
  expectedUseAmount: [{ required: true, message: '请输入拟用信金额' }],
  nominalInterestRate: [{ required: true, message: '请输入合同利率' }],
  expectedLaunchDate: [{ required: true, message: '请选择预计业务投放日', trigger: 'change' }],
  expectedDueDate: [{ required: true, message: '请选择预估最后还款日', trigger: 'change' }],
  principalRepaymentMethod: [{ required: true, message: '请选择还本方式', trigger: 'change' }],
  interestRepaymentMethod: [{ required: true, message: '请选择还息方式', trigger: 'change' }],
  principalPeriod: [{ required: true, message: '请选择分期还本频次', trigger: 'change' }],
  interestPeriod: [{ required: true, message: '请选择分期还息频次', trigger: 'change' }],
  repayPrincipalDay: [{ required: true, message: '请选择默认当期还本日', trigger: 'change' }],
  repayInterestDay: [{ required: true, message: '请选择默认当期还息日', trigger: 'change' }],
};
const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const colSpan = COL_SPAN_PROP;
const fullProp = { ...FULL_FORM_ITEM_PROP, labelCol: { span: 4 }, wrapperCol: { span: 20 } };
</script>

<template>
  <BasicPopup v-bind="$attrs" title="用信定价" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" :loading="loading.submit" @click="save('save')">保存</a-button>
        <a-button type="primary" :loading="loading.submit" @click="save('submit')">提交</a-button>
      </a-space>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="FormRef" class="mt-5" :model="creditPricingForm" :rules="rules" v-bind="formProp">
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="关联综合授信项目" name="projectId">
              <ApiComponent
                v-model="creditPricingForm.projectId as unknown as string"
                :component="Select"
                :api="getProjectListApi"
                :params="{ status: 'EFFECTIVE', isMeetingCompleted: 1 }"
                label-field="projectName"
                value-field="id"
                model-prop-name="value"
                :after-fetch="
                  (res) => {
                    return res.filter((item: any) => item.projectType === 'comprehensive');
                  }
                "
                @change="selectInitiation"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目类型" name="projectType">
              <a-select
                v-model:value="creditPricingForm.projectType"
                :options="dictStore.getDictList('FCT_PROJECT_TYPE')"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="项目定价名称" name="pricingName">
              <a-input v-model:value="creditPricingForm.pricingName" />
            </a-form-item>
          </a-col>
          <!--          <a-col v-bind="colSpan">-->
          <!--            <a-form-item label="授信企业" name="creditCompanyCode">-->
          <!--              <ApiComponent-->
          <!--                v-model="creditPricingForm.creditCompanyCode as unknown as string"-->
          <!--                :component="Select"-->
          <!--                :api="getCompanyListApi"-->
          <!--                label-field="companyName"-->
          <!--                value-field="companyCode"-->
          <!--                model-prop-name="value"-->
          <!--                @change="(value: number, data: any) => selectCompany(value, data, 'creditCompanyName')"-->
          <!--              />-->
          <!--            </a-form-item>-->
          <!--          </a-col>-->
          <a-col v-bind="colSpan">
            <a-form-item label="客户类别" name="customerCategory">
              <a-select
                v-model:value="creditPricingForm.customerCategory"
                :options="dictStore.getDictList('FCT_COMPANY_LIMIT_COMPANY_TYPE')"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="客户分级" name="customerLevel">
              <a-select
                v-model:value="creditPricingForm.customerLevel"
                :options="dictStore.getDictList('COMPANY_RATING')"
                disabled
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="行业属性" name="industryAttributes">
              <a-input v-model:value="creditPricingForm.industryAttributes" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信额度测算结果" name="creditCalculateAmount">
              <a-input v-model:value="creditPricingForm.creditCalculateAmount" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="区域特性" name="areaCharacteristics">
              <a-input v-model:value="creditPricingForm.areaCharacteristics" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="底层项目" name="bottomProject">
              <a-input v-model:value="creditPricingForm.bottomProject" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="担保方式" name="guaranteeMethodDesc">
              <a-input v-model:value="creditPricingForm.guaranteeMethodDesc" />
            </a-form-item>
          </a-col>
          <a-col :span="24">
            <a-form-item label="其他情况" name="pricingOtherDesc" v-bind="fullProp">
              <a-textarea v-model:value="creditPricingForm.pricingOtherDesc" :rows="4" class="w-full" />
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 授信批复方案 -->
        <BasicCaption content="授信批复方案" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="授信金额（元）" name="pricingCreditAmount">
              <a-input v-model:value="creditPricingForm.pricingCreditAmount" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信期限（个月）" name="pricingCreditTerm">
              <a-input v-model:value="creditPricingForm.pricingCreditTerm" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信费率（%/年）" name="pricingCreditRate">
              <a-input v-model:value="creditPricingForm.pricingCreditRate" disabled />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信额度类型">
              {{ dictStore.formatter(creditPricingForm.pricingCreditType, 'FCT_CREDIT_TYPE') }}
            </a-form-item>
          </a-col>
        </a-row>

        <!-- 用信定价方案 -->
        <BasicCaption content="用信定价方案" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="用信主体" name="creditUseCompanyCode">
              <ApiComponent
                v-model="creditPricingForm.creditUseCompanyCode as unknown as string"
                :component="Select"
                :api="getCompanyListApi"
                label-field="companyName"
                value-field="companyCode"
                model-prop-name="value"
                @change="(value: number, data: any) => selectCompany(value, data, 'creditUseCompanyName')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="基准利率（%/年）" name="pricingBasicRatio">
              <a-input v-model:value="creditPricingForm.pricingBasicRatio" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="用信定价综合收益率(%/年)" name="pricingXirrRate">
              <a-input v-model:value="creditPricingForm.pricingXirrRate" />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="还本付息方案" />

        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="宽限期天数（天）" name="gracePeriodDays">
              <a-input v-model:value="creditPricingForm.gracePeriodDays" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="宽限期费率（%/年）" name="gracePeriodRate">
              <a-input v-model:value="creditPricingForm.gracePeriodRate" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="服务费（元）" name="serviceFeeAmount">
              <a-input v-model:value="creditPricingForm.serviceFeeAmount" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="罚息类型" name="penaltyType">
              <a-radio-group
                v-model:value="creditPricingForm.penaltyType"
                :options="dictStore.getDictList('FCT_PENALTY_TYPE')"
              />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan" v-if="creditPricingForm.penaltyType === 'fixed'">
            <a-form-item label="固定罚息利率（%）" name="penaltyInterestRate">
              <a-input v-model:value="creditPricingForm.penaltyInterestRate" />
            </a-form-item>
          </a-col>
        </a-row>
        <LadderPenalty
          ref="LadderPenaltyRef"
          v-show="creditPricingForm.penaltyType === 'ladder'"
          v-model="creditPricingForm"
        />
        <RepaymentCalculation
          ref="RepaymentCalculationRef"
          v-model="creditPricingForm"
          calculation-type="CreditPricing"
        />
        <BaseAttachmentList
          v-model="creditPricingForm.attachmentList"
          :business-id="creditPricingForm.id"
          business-type="FCT_PROJECT_CREDIT_PRICING"
          edit-mode
        />
      </a-form>
    </div>
  </BasicPopup>
</template>

<style></style>
