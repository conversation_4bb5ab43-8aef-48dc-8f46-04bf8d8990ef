<script setup lang="ts">
import type { OverviewInfo, ContractInfo } from '#/api';
import type { Rule } from 'ant-design-vue/es/form';
import { computed, reactive, ref } from 'vue';
import { BasicCaption } from '@vben/fe-ui';
import { BASE_PAGE_CLASS_NAME, COL_SPAN_PROP } from '@vben/constants';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';

import { Button } from 'ant-design-vue';
import { getProjectContractDetail } from '#/api';
import { ContractList } from '#/components';
import { FORM_PROP } from '@vben/constants';
import { cloneDeep } from 'lodash-es';
import dayjs from 'dayjs';
const emit = defineEmits(['register', 'ok']);

const formProp = { ...FORM_PROP, labelCol: { span: 8 }, wrapperCol: { span: 16 } };
const colSpan = COL_SPAN_PROP;
const contractForm = ref<ContractInfo>({
  contractAmount: undefined,
  contractCode: '',
  contractEndDate: undefined,
  contractList: [],
  contractStartDate: undefined,
  contractTerm: undefined,
  creditAmount: undefined,
  creditExpireDate: '',
  creditTerm: undefined,
  decisionDate: '',
  projectId: undefined,
  signDate: '',
});

const title = computed(() => {
  return contractForm.value.contractList?.length > 0 ? '编辑' : '新增';
});
const contractDateRange = computed({
  get() {
    return [contractForm.value.contractStartDate, contractForm.value.contractEndDate];
  },
  set(newValue) {
    if (Array.isArray(newValue) && newValue.length === 2) {
      contractForm.value.contractStartDate = newValue[0];
      contractForm.value.contractEndDate = newValue[1];
    }
  }
})
const projectType = ref<string | undefined>('');
// 添加初始化方法
const init = async (data: any) => {
  contractForm.value.projectId = data?.projectId as number;
  projectType.value = data?.projectType;
  const res = await getProjectContractDetail(contractForm.value.projectId as number);
  if (res) {
    res.contractEndDate = dayjs(res.contractEndDate).valueOf()
    res.contractStartDate = dayjs(res.contractStartDate).valueOf()
    res.decisionDate = dayjs(res.decisionDate).valueOf().toString()
    res.creditExpireDate = dayjs(res.creditExpireDate).valueOf().toString()
    res.signDate = dayjs(res.signDate).valueOf().toString()
    contractForm.value = cloneDeep(res)
  }
};
const rules: Record<string, Rule[]> = {
  decisionDate: [{ required: true, message: '请输入决策日期', trigger: 'change' }],
  creditTerm: [{ required: true, message: '请输入授信期限' }],
  creditExpireDate: [{ required: true, message: '请输入授信到期日', trigger: 'change' }],
  creditAmount: [{ required: true, message: '请输入授信金额' }],
  contractCode: [{ required: true, message: '请输入合同编号' }],
  signDate: [{ required: true, message: '请选择合同签订日期', trigger: 'change' }],
  contractStartDate: [{ required: true, message: '请选择合同开始日期', trigger: 'change' }],
  contractTerm: [{ required: true, message: '请输入合同期限' }],
  contractAmount: [{ required: true, message: '请输入合同金额' }],
};
const [registerPopup, { changeOkLoading }] = usePopupInner((data) => init(data));
const formRef = ref()
const handleSave = async () => {
  try {
    await formRef.value.validate();
    const params = cloneDeep(contractForm.value)
    params.contractStartDate = contractDateRange.value[0]
    params.contractEndDate = contractDateRange.value[1]
    emit('ok', params);
  } catch (error) {
    console.error('提交失败:', error);
  } finally {
    changeOkLoading(false);
  }
};

const submit = async () => {
  handleSave();
};
const handleClose = () => {
  contractForm.value = {}
};
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup" @close="handleClose">
    <template #centerToolbar>
      <Button class="ml-2" type="primary" @click="submit">提交</Button>
    </template>
    <div :class="BASE_PAGE_CLASS_NAME">
      <a-form ref="formRef" :model="contractForm" :rules="rules" v-bind="formProp" class="">
        <BasicCaption v-if="projectType === 'single'" content="总经办决策" />
        <a-row v-if="projectType === 'single'" class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="决策日期" name="decisionDate">
              <a-date-picker v-model:value="contractForm.decisionDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信期限（个月）" name="creditTerm">
              <a-input-number v-model:value="contractForm.creditTerm" :controls="false" class="w-full" :precision="0" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信到期日" name="creditExpireDate">
              <a-date-picker v-model:value="contractForm.creditExpireDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="授信额度（元）" name="creditAmount">
              <a-input-number v-model:value="contractForm.creditAmount" :controls="false" class="w-full"
                :precision="0" />
            </a-form-item>
          </a-col>
        </a-row>
        <BasicCaption content="主合同信息" />
        <a-row class="mt-5">
          <a-col v-bind="colSpan">
            <a-form-item label="保理合同编号" name="contractCode">
              <a-input v-model:value="contractForm.contractCode" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="签署日期" name="signDate">
              <a-date-picker v-model:value="contractForm.signDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="合同日期范围" name="contractStartDate">
              <a-range-picker v-model:value="contractDateRange" value-format="x" class="w-full" />
            </a-form-item>
          </a-col>
          <!-- <a-col v-bind="colSpan">
            <a-form-item label="合同结束日期" name="contractEndDate">
              <a-date-picker v-model:value="contractForm.contractEndDate" value-format="x" class="w-full" />
            </a-form-item>
          </a-col> -->
          <a-col v-bind="colSpan">
            <a-form-item label="合同期限（个月）" name="contractTerm">
              <a-input-number v-model:value="contractForm.contractTerm" :controls="false" class="w-full"
                :precision="0" />
            </a-form-item>
          </a-col>
          <a-col v-bind="colSpan">
            <a-form-item label="合同金额（元）" name="contractAmount">
              <a-input-number v-model:value="contractForm.contractAmount" :controls="false" class="w-full"
                :precision="0" />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <ContractList v-model="contractForm.contractList" :business-id="contractForm.projectId"
        business-type="FCT_PROJECT_CONTRACT" edit-mode />
    </div>
  </BasicPopup>
</template>

<style scoped>
:deep(.fileBtn) div {
  display: none !important;
}

.fileBtn {
  margin-right: 10px;

  .mb-1 {
    margin-bottom: 0;
  }
}
</style>
