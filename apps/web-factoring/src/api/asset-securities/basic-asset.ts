import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface BasicAssetInfo {
  /**
   * ABS基础资产编号
   */
  absAssetCode?: string;
  /**
   * ABS基础资产名称
   */
  absAssetName?: string;
  /**
   * 资产五级分类：正常/关注/次级/可疑/损失
   */
  assetClassification?: string;
  /**
   * 基础资产状态
   */
  assetStatus?: string;
  /**
   * 债权人
   */
  creditorName?: string;
  /**
   * 债务人
   */
  debtorName?: string;
  /**
   * 保理合同编号
   */
  factoringContractCode?: string;
  /**
   * 融资金额
   */
  financingAmount?: number;
  /**
   * 融资利率
   */
  financingRate?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 应收账款账龄(天)
   */
  receivableAgeDays?: number;
  /**
   * 应收账款金额
   */
  receivableAmount?: number;
  /**
   * 应收账款
   */
  receivableList?: AbsAssetReceivableBO[];
  /**
   * 剩余天数（天）
   */
  remainingDays?: number;
  /**
   * 操作状态
   */
  status?: string;
  [property: string]: any;
}

/**
 * AbsAssetReceivableBO，ABS基础资产-应收账款-关联表业务实体类
 */
export interface AbsAssetReceivableBO {
  /**
   * 基础资产ID
   */
  absAssetId?: number;
  /**
   * 资产五级分类：正常/关注/次级/可疑/损失
   */
  assetClassification?: string;
  /**
   * 业务类型
   */
  bizType?: string;
  /**
   * 债权人
   */
  creditorName?: string;
  /**
   * 债务人
   */
  debtorName?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 应收账款金额
   */
  receivableAmount?: number;
  /**
   * 应收账款到期日
   */
  receivableDueDate?: Date;
  /**
   * 应收账款ID
   */
  receivableId?: number;
  /**
   * 应收账款名称
   */
  receivableName?: string;
  [property: string]: any;
}

// 获取分页列表
export async function getBasicAssetPageListApi(params: PageListParams) {
  return requestClient.get<BasicAssetInfo[]>('/factoring/abs/asset/page', { params });
}

// 添加
export async function addBasicAssetApi(data: BasicAssetInfo) {
  return requestClient.post<BasicAssetInfo>('/factoring/abs/asset/add', data);
}

// 编辑
export async function editBasicAssetApi(data: BasicAssetInfo) {
  return requestClient.post<BasicAssetInfo>('/factoring/abs/asset/edit', data);
}

// 批量入池
export async function batchBasicInputApi(data: BasicAssetInfo) {
  return requestClient.post<BasicAssetInfo>(`/factoring/abs/asset/batch/input?poolId=${data.poolId}`, data.assetIds);
}

// 入池
export async function basicInputApi(data: BasicAssetInfo) {
  return requestClient.post<BasicAssetInfo>(`/factoring/abs/asset/input?poolId=${data.poolId}&assetId=${data.assetId}`);
}

// 获取详情
export async function getBasicAssetInfoApi(params: BasicAssetInfo) {
  return requestClient.get<BasicAssetInfo[]>('/factoring/abs/asset/detail', { params });
}

// 获取列表
export async function basicAssetListApi(params: BasicAssetInfo) {
  return requestClient.get<BasicAssetInfo>('/factoring/abs/asset/list', { params });
}

// 删除
export async function delBasicAssetApi(id: string) {
  return requestClient.post('/factoring/abs/asset/delete', {}, { params: { id } });
}

// 查询项目和应收账款信息
export async function getAssetProjectIdInfoApi(params: BasicAssetInfo) {
  return requestClient.get(`/factoring/abs/asset/info/${params.projectId}?creditApplyId=${params.creditApplyId}`);
}
