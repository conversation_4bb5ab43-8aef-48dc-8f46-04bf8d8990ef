import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface OverviewInfo {
  /**
   * 项目背景说明
   */
  backgroundDesc?: string;
  /**
   * 业务经理ids
   */
  businessUserIds?: string;
  /**
   * 业务经理
   */
  businessUserNames?: string;
  /**
   * 地市编码
   */
  cityCode?: string;
  /**
   * 地市
   */
  cityName?: string;
  /**
   * 关联企业列表
   */
  companyList?: ProjectCompanyBO[];
  /**
   * 授信额度（元）
   */
  creditAmount?: number;
  /**
   * 客户测算额度（元）
   */
  creditCalculateAmount?: number;
  /**
   * 增信措施说明
   */
  creditEnhancementDesc?: string;
  /**
   * 额度说明
   */
  creditLimitDesc?: string;
  /**
   * 最低用信期限（个月）
   */
  creditMinPeriod?: number;
  /**
   * 债权人基本情况说明
   */
  creditorInfoDesc?: string;
  /**
   * 其他情况说明
   */
  creditOtherDesc?: string;
  /**
   * 授信费率（%）
   */
  creditRate?: number;
  /**
   * 授信对象
   */
  creditRecipient?: string;
  /**
   * 授信对象基本情况
   */
  creditRecipientDesc?: string;
  /**
   * 单笔用信额度上限（元）
   */
  creditSingleMaxUsed?: number;
  /**
   * 授信期限（月）
   */
  creditTerm?: number;
  /**
   * 是否循环额度（授信方式）
   */
  creditType?: string;
  /**
   * 债务人基本情况说明
   */
  debtorInfoDesc?: string;
  /**
   * 区县编码
   */
  districtCode?: string;
  /**
   * 区县
   */
  districtName?: string;
  /**
   * 预估综合收益率（%）
   */
  estimateIncomeRate?: number;
  /**
   * 支持保理方向
   */
  factoringDirection?: string;
  /**
   * 保理类型
   */
  factoringType?: string;
  /**
   * 财务经理ids
   */
  financeUserIds?: string;
  /**
   * 财务经理
   */
  financeUserNames?: string;
  /**
   * 保理融资金额（元）
   */
  financingAmount?: number;
  /**
   * 保理融资比例（%）
   */
  financingRatio?: number;
  /**
   * 资金用途说明
   */
  fundUsageDesc?: string;
  /**
   * 担保人基本情况说明
   */
  guarantorInfoDesc?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 是否为省重点产业：0-否 1-是
   */
  isProvincialKeyIndustry?: number;
  /**
   * 是否审批
   */
  isReview?: number;
  /**
   * 是否支持实体经济：0-否 1-是
   */
  isSupportRealEconomy?: number;
  /**
   * 总经办会议状态
   */
  meetingBranchStatus?: string;
  /**
   * 总经办会议状态
   */
  meetingGeneralStatus?: string;
  /**
   * 项目评审会状态
   */
  meetingReviewStatus?: string;
  /**
   * 抵押物关系
   */
  mortgageList?: ProjectMortgageRefBO[];
  /**
   * 运营经理ids
   */
  operationsUserIds?: string;
  /**
   * 运营经理
   */
  operationsUserNames?: string;
  /**
   * 质押物关系
   */
  pledgeList?: ProjectPledgeRefBO[];
  /**
   * 项目编号
   */
  projectCode?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 项目类型
   */
  projectType?: string;
  /**
   * 应收账款金额（元）
   */
  receivableAmount?: number;
  /**
   * 应收账款描述
   */
  receivableDesc?: string;
  /**
   * 应收账款关系
   */
  receivableRefList?: ProjectReceivableRefBO[];
  /**
   * 追索权要求
   */
  recourseRequired?: string;
  /**
   * 审批状态
   */
  reviewStatus?: string;
  /**
   * 风控措施描述
   */
  riskControlDesc?: string;
  /**
   * 风控经理ids
   */
  riskUserIds?: string;
  /**
   * 风控经理
   */
  riskUserNames?: string;
  /**
   * 服务费（元）
   */
  serviceAmount?: number;
  /**
   * 操作状态
   */
  status?: string;
  /**
   * 支持操作模式
   */
  supportMode?: string;
  /**
   * 项目存续状态
   */
  survivalStatus?: string;
  /**
   * 合作企业Code
   */
  targetCompanyCode?: string;
  /**
   * 合作企业名称
   */
  targetCompanyName?: string;
  /**
   * 投向行业
   */
  targetIndustry?: string;
  /**
   * 关联用户列表
   */
  userList?: ProjectUserBO[];
  [property: string]: any;
  /**
   * 合同状态
   */
  contractReviewStatus?: string;
}
/**
 * ProjectCompanyBO，项目主体企业
 */
export interface ProjectCompanyBO {
  /**
   * 企业编码
   */
  companyCode?: string;
  /**
   * 企业名称
   */
  companyName?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 项目企业类型
   */
  projectCompanyType?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  [property: string]: any;
}

/**
 * ProjectMortgageRefBO，项目抵押物关联实体类
 */
export interface ProjectMortgageRefBO {
  /**
   * 抵押物评估价值
   */
  assessedAmount?: number;
  /**
   * 主键
   */
  id?: number;
  /**
   * 出证日期
   */
  issuingDate?: Date;
  /**
   * 抵押价值/最高债权数额
   */
  mortgageAmount?: number;
  /**
   * 抵押物编号
   */
  mortgageCode?: string;
  /**
   * 抵押方code
   */
  mortgageCompanyCode?: string;
  /**
   * 抵押方
   */
  mortgageCompanyName?: string;
  /**
   * 抵押权方code
   */
  mortgageeCompanyCode?: string;
  /**
   * 抵押权方
   */
  mortgageeCompanyName?: string;
  /**
   * 抵押物ID
   */
  mortgageId?: number;
  /**
   * 抵押物名称
   */
  mortgageName?: string;
  /**
   * 抵押方式
   */
  mortgageType?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 不动产坐落
   */
  propertyLocation?: string;
  /**
   * 他项权证编号
   */
  rightsCertNo?: string;
  [property: string]: any;
}

/**
 * ProjectPledgeRefBO，项目质押物关联实体类
 */
export interface ProjectPledgeRefBO {
  /**
   * 债务履行期限结束
   */
  endDebtPeriodDate?: Date;
  /**
   * 主键
   */
  id?: number;
  /**
   * 主合同金额
   */
  mainContractAmount?: number;
  /**
   * 质押主合同号码
   */
  mainContractCode?: string;
  /**
   * 质押财产价值
   */
  pledgeAmount?: number;
  /**
   * 质押物编号
   */
  pledgeCode?: string;
  /**
   * 质押合同编号
   */
  pledgeContractCode?: string;
  /**
   * 质权方编码
   */
  pledgeeCompanyCode?: string;
  /**
   * 质权方名称
   */
  pledgeeCompanyName?: string;
  /**
   * 质押物ID
   */
  pledgeId?: number;
  /**
   * 质押物名称
   */
  pledgeName?: string;
  /**
   * 出质方编码
   */
  pledgorCompanyCode?: string;
  /**
   * 出质方名称
   */
  pledgorCompanyName?: string;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 债务履行期限开始
   */
  startDebtPeriodDate?: Date;
  [property: string]: any;
}

/**
 * ProjectReceivableRefBO，项目应收账款关系
 */
export interface ProjectReceivableRefBO {
  /**
   * 业务类型
   */
  bizType?: string;
  /**
   * 池保理融资企业
   */
  companyName?: string;
  /**
   * 债权人、债务人关系
   */
  creditorDebtorDel?: string;
  /**
   * 债权人
   */
  creditorName?: string;
  /**
   * 债务人
   */
  debtorName?: string;
  /**
   * 主键
   */
  id?: number;
  /**
   * 应收账款池编号
   */
  poolCode?: string;
  /**
   * 应收账款池到期日
   */
  poolDueDate?: Date;
  /**
   * 池融资比例
   */
  poolFinanceRate?: number;
  /**
   * 应收账款池名称
   */
  poolName?: string;
  /**
   * 池内资产总额
   */
  poolTotalAmount?: number;
  /**
   * 应收账款池有效期（月）
   */
  poolValidity?: number;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目名称
   */
  projectName?: string;
  /**
   * 应收账款金额
   */
  receivableAmount?: number;
  /**
   * 应收账款编号
   */
  receivableCode?: string;
  /**
   * 应收账款到期日
   */
  receivableDueDate?: Date;
  /**
   * 应收账款ID
   */
  receivableId?: number;
  /**
   * 应收账款名称
   */
  receivableName?: string;
  /**
   * 应收账款池ID
   */
  receivablePoolId?: number;
  /**
   * 应收账款期限(月)
   */
  receivableTerm?: number;
  /**
   * 提交日期
   */
  submitDate?: Date;
  [property: string]: any;
}

/**
 * ProjectUserBO，项目管理人员
 */
export interface ProjectUserBO {
  /**
   * 主键
   */
  id?: number;
  /**
   * 项目ID
   */
  projectId?: number;
  /**
   * 项目人员类型
   */
  projectUserType?: string;
  /**
   * 用户ID
   */
  userId?: number;
  /**
   * 用户名称
   */
  userName?: string;
  [property: string]: any;
}
export interface ContractInfo {
  /**
     * 合同金额
     */
    contractAmount?: number;
    /**
     * 保理合同编号
     */
    contractCode?: string;
    /**
     * 合同结束日期
     */
    contractEndDate?: number;
    /**
     * 合同绑定
     */
    contractList?: contractVO[];
    /**
     * 合同开始日期
     */
    contractStartDate?: number;
    /**
     * 合同期限（个月）
     */
    contractTerm?: number;
    /**
     * 授信额度
     */
    creditAmount?: number;
    /**
     * 授信到期日
     */
    creditExpireDate?: string;
    /**
     * 授信期限（个月）
     */
    creditTerm?: number;
    /**
     * 决策日期
     */
    decisionDate?: string;
    /**
     * 项目ID
     */
    projectId: number | undefined;
    /**
     * 签署日期
     */
    signDate?: string;
    [property: string]: any;
}

export interface contractVO {
  /**
   * 合同主键
   */
  id?: number;
  /**
   * 合同类型 1 常规合同 0 临时合同
   */
  type?: number;
  [property: string]: any;
}

// 获取项目信息分页列表
export async function getOverviewPageListApi(params: PageListParams) {
  return requestClient.get<OverviewInfo[]>('/factoring/project/page', { params });
}

// 获取项目信息详情
export async function getOverviewInfoApi(id: number) {
  return requestClient.get<OverviewInfo>(`/factoring/project/detail/${id}`);
}

// 获取项目列表
export async function getProjectListApi(params: PageListParams) {
  return requestClient.get('/factoring/project/list', { params });
}

// 历史记录-分页列表
export async function getOverviewLogListApi(params: PageListParams) {
  return requestClient.get<OverviewInfo[]>('/factoring/project/log/page', { params });
}

// 历史记录-详情
export async function getOverviewLogApi(id: number) {
  return requestClient.get<OverviewInfo>(`/factoring/project/log/detail/${id}`);
}

// 变更状态
export async function getChangeStatusApi(id: number) {
  return requestClient.post(`/factoring/project/change/${id}`);
}

// 删除
export async function delOverviewApi(id: number) {
  return requestClient.post(`/factoring/project/delete/${id}`);
}

// 变更综合项目
export async function editComprehensiveOverviewApi(data: OverviewInfo) {
  return requestClient.post<OverviewInfo>('/factoring/project/comprehensive/edit', data);
}

// 变更单一
export async function editSingleOverviewApi(data: OverviewInfo) {
  return requestClient.post<OverviewInfo>('/factoring/project/single/edit', data);
}

// 项目人员移交
export async function transferUserProject(data: OverviewInfo) {
  return requestClient.post<OverviewInfo>('/factoring/project/transfer/user', data);
}

//
export async function bindingProjectByContract(data: ContractForm) {
  return requestClient.post('/factoring/contract/edit', data);
}


export async function getProjectContractDetail(id: number) {
  return requestClient.get<ContractForm>(`/factoring/contract/detail/${id}`);
}

export async function getOverviewContractListApi(params: any) {
  return requestClient.get<OverviewInfo>('/base/contract/list/biz', { params });
}
