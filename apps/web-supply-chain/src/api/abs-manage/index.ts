import type { PageListParams } from '@vben/types';

import { requestClient } from '#/api/request';

export interface ABSManageInfo {
  id?: null | string;
  absProjectCode?: string;
  absProjectName?: string;
  projectId?: number | string;
  projectName?: string;
  projectDate?: string;
  approvalStatus?: string;
  status?: string;
  remark?: string;
  attachmentList?: string;
  latestDate?: string;
}

export async function getABSListApi(params: PageListParams) {
  return requestClient.get('/scm/abs/manager/page', { params });
}
export async function addABSApi(params: ABSManageInfo) {
  return requestClient.post('/scm/abs/manager/add', params);
}
export async function submitABSApi(params: ABSManageInfo) {
  return requestClient.post('/scm/abs/manager/submit', params);
}
export async function editABSApi(params: ABSManageInfo) {
  return requestClient.post('/scm/abs/manager/edit', params);
}
export async function deleteABSApi(id: string) {
  return requestClient.post(`/scm/abs/manager/delete/${id}`);
}
export async function cancelABSApi(id: string) {
  return requestClient.post(`/scm/abs/manager/cancel/${id}`);
}
export async function detailABSApi(id: string) {
  return requestClient.get(`/scm/abs/manager/detail/${id}`);
}
