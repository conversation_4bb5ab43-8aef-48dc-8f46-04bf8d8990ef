<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';

import type { AddShipment, SignShipment } from '#/api';

import { h, reactive } from 'vue';

import { Page } from '@vben/common-ui';
import { usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import { VbenIcon } from '@vben-core/shadcn-ui';

import { Modal as AntdModal, Button, DatePicker, FormItem, Space, TypographyLink, Upload } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { cancelShipmentApi, deleteShipmentApi, getShipmentPageApi, signShipmentApi } from '#/api';

import Create from './create.vue';

const dictStore = useDictStore();

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'shipmentCode',
      label: '发运单编号',
    },
    {
      component: 'Select',
      fieldName: 'transportMethod',
      label: '运输方式',
      // componentProps: {
      //   options: dictStore.getDictList('business_structure'), // 补全字典类型
      // },
    },
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '所属项目编码',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '所属项目名称',
    },
    {
      component: 'Select',
      fieldName: 'carrierCompanyName',
      label: '运输企业名称',
      // componentProps: {
      //   options: dictStore.getDictList('supplier_company'), // 补全字典类型
      // },
    },
    // {
    //   component: 'Select',
    //   fieldName: 'warehouseName',
    //   label: '关联单据类型',
    //   // componentProps: {
    //   //   options: dictStore.getDictList('purchaser_company'), // 补全字典类型
    //   // },
    // },
    // {
    //   component: 'Input',
    //   fieldName: 'detailAddress',
    //   label: '关联单据编号',
    // },
    {
      component: 'Select',
      fieldName: 'status',
      label: '业务状态',
      componentProps: {
        options: dictStore.getDictList('BUS_STATUS'),
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'shipmentDate',
      label: '发运日期',
    },
  ],
  fieldMappingTime: [
    // 将 shipmentDate 数组映射到 shipmentStartDate 和 shipmentEndDate 字段
    ['shipmentDate', ['shipmentStartDate', 'shipmentEndDate'], 'YYYY-MM-DD'],
  ],
  showCollapseButton: true,
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    { field: 'shipmentId', title: '关联的发货单ID' },
    { field: 'deliveryType', title: '发货类型' },
    { field: 'deliveryNumber', title: '车辆号/船舶号/物流号' },
    { field: 'contactName', title: '联系人姓名' },
    { field: 'contactPhone', title: '联系人电话' },
    { field: 'createTime', title: '创建时间' },
    { field: 'createBy', title: '创建人' },
    { field: 'updateTime', title: '更新时间' },
    { field: 'updateBy', title: '更新人' },
    { field: 'remarks', title: '备注' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 260,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getShipmentPageApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const [registerForm, { openPopup: openFormPopup }] = usePopup();
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

// 创建
const add = () => {
  openFormPopup(true, {});
};

// 编辑
const handleEdit = (row: AddShipment) => {
  openFormPopup(true, row);
  console.log('编辑', row);
};

// 查看
const handleDetail = (row: AddShipment) => {
  openFormPopup(true, row);
  console.log('查看', row);
};

// 删除
const del = (row: AddShipment) => {
  AntdModal.confirm({
    title: $t('base.confirmDelete'),
    content: $t('base.deleteConfirmContent'),
    okText: $t('base.confirm'),
    cancelText: $t('base.cancel'),
    onOk: async () => {
      // 这里可以调用删除API
      await deleteShipmentApi(row.id);
      // 删除成功后刷新列表
      await gridApi.reload();
    },
  });
};

// 作废
const cancel = (row: AddShipment) => {
  AntdModal.confirm({
    title: $t('base.confirmCancel'),
    content: $t('base.cancelConfirmContent'),
    okText: $t('base.confirm'),
    cancelText: $t('base.cancel'),
    onOk: async () => {
      // 这里可以调用删除API
      await cancelShipmentApi(row.id);
      // 删除成功后刷新列表
      await gridApi.reload();
    },
  });
};

// 下载
const download = (row: AddShipment) => {
  console.log('下载', row);
};

const signData = reactive<SignShipment>({
  id: undefined,
  createTime: '2025-01-01',
  createBy: 0,
  updateTime: '2025-01-02',
  updateBy: 0,
  deleteFlag: true,
  version: 0,
  shipmentId: 'test',
  signDate: '2025-02-02',
  signerName: 'test',
  receiptFileId: 'test',
  remarks: 'test',
});

// 签收
const confirmAccept = (row: AddShipment) => {
  const contentVNode = h('div', { class: 'p-4 space-y-4' }, [
    h(FormItem, { label: '收货日期' }, [
      h(DatePicker, {
        style: { width: '100%' },
        modelValue: signData.signDate,
        'onUpdate:modelValue': (value: string) => {
          signData.signDate = value;
        },
      }),
    ]),
    h(FormItem, { label: '上传文件' }, [
      h(
        Upload,
        {
          beforeUpload: () => false,
          fileList: [],
        },
        [h(Button, { type: 'primary' }, '选择文件')],
      ),
    ]),
  ]);

  Modal.info({
    title: '确认收货',
    okText: $t('base.confirm'),
    cancelText: $t('base.cancel'),
    width: 600,
    content: () => contentVNode,
    onOk: async () => {
      await signShipmentApi({ ...signData, id: row.id });
      await gridApi.reload();
    },
    closable: true,
  });
};

const editSuccess = () => {
  gridApi.formApi.submitForm();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <Button class="mr-2" type="primary" @click="add">
          <VbenIcon icon="ant-design:plus-outlined" class="mr-1 text-base" />
          {{ $t('base.add') }}
        </Button>
      </template>
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="handleEdit(row)">
            {{ $t('base.edit') }}
          </TypographyLink>
          <TypographyLink @click="handleDetail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="del(row)">
            {{ $t('base.del') }}
          </TypographyLink>
          <TypographyLink type="danger" @click="cancel(row)">作废 </TypographyLink>
          <TypographyLink @click="download(row)"> {{ $t('base.download') }} </TypographyLink>
          <TypographyLink @click="confirmAccept(row)"> {{ $t('base.confirmAccept') }} </TypographyLink>
        </Space>
      </template>
    </Grid>
    <Create @register="registerForm" @ok="editSuccess" />
  </Page>
</template>

<style></style>
