<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { GridApi, VxeTableGridOptions } from '#/adapter/vxe-table';

import { computed, reactive, ref } from 'vue';

import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import BasicCaption from '@vben/fe-ui/components/Basic/src/BasicCaption.vue';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defaultsDeep } from '@vben/utils';

import { Button, Col, DatePicker, Form, FormItem, Input, message, Row, Select, Textarea } from 'ant-design-vue';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  type ShipmentBaseInfo,
  addShipmentApi,
  editShipmentApi,
  detailShipmentApi,
  projectManageListApi,
  getCompanyApi,
  getOrderCodesApi,
} from '#/api';
import { BASE_PAGE_CLASS_NAME } from '@vben/constants';
import { BaseAttachmentList, BaseRegionPicker } from '#/adapter/base-ui';

const emit = defineEmits(['register', 'ok']);

// 字典选项
const businessStructureOptions = ref([
  { label: '先采后销', value: '1' },
  { label: '先销后采', value: '2' },
]);

const projectModelOptions = ref([
  { label: '建材模式', value: '1' },
  { label: '产业模式', value: '2' },
]);

const executorCompanyOptions = ref([
  { label: '上海负责人', value: '1' },
  { label: '北京负责人', value: '2' },
]);

const { getDictList } = useDictStore();

interface SelectOption {
  projectName: string | undefined;
  id: number | string | undefined;
}
const projectInfoOptions = ref<SelectOption[]>([]);

// 默认数据
const defaultForm: Partial<ShipmentBaseInfo> = {
  id: undefined,
  createTime: undefined,
  createBy: undefined,
  updateTime: undefined,
  updateBy: undefined,
  version: undefined,
  shipmentCode: undefined,
  projectId: undefined,
  projectName: undefined,
  projectCode: undefined,
  shipmentType: undefined,
  transportMethod: undefined,
  carrierCompanyCode: undefined,
  carrierCompanyName: undefined,
  consigneeCompanyCode: undefined,
  consigneeCompanyName: undefined,
  billingCompanyCode: undefined,
  billingCompanyName: undefined,
  shipmentDate: undefined,
  status: undefined,
  totalShipmentCost: undefined,
  province: undefined,
  city: undefined,
  district: undefined,
  detailAddress: undefined,
  plannedDeliveryDate: undefined,
  receiptDistrict: undefined,
  receiptDetailAddress: undefined,
  receiptProvince: undefined,
  receiptCity: undefined,
  remarks: undefined,
  shipmentDeliveryList: [],
  shipmentItemList: [],
  shipmentSourceRelList: [],
  attachmentList: [],
};

let detailForm = reactive<Partial<ShipmentBaseInfo>>(defaultsDeep(defaultForm));

const colSpan = { md: 12, sm: 24 };

const rules: Record<string, Rule[]> = {
  // inboundReceiptCode: [{ required: true, message: '入库单编号', trigger: 'change' }],
  // projectName: [{ required: true, message: '所属项目名称', trigger: 'change' }],
  // sourceDocumentType: [{ required: true, message: '关联单据类型', trigger: 'change' }],
  // deliveryReceiptId: [{ required: true, message: '关联单据', trigger: 'change' }],
  // receiptDate: [{ required: true, message: '入库日期', trigger: 'change' }],
  // customerCompanyName: [{ required: true, message: '上/下游企业', trigger: 'change' }],
  // warehouseName: [{ required: true, message: '仓库名称', trigger: 'change' }],
};

const companyOptions = ref([]);
const orderCodesOptions = ref([]);
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};
// 获取关联单据列表
const getOrderCodesList = async () => {
  if (detailForm.documentType === 'PICKUP_REQUEST') {
    message.error('先不要选提货单,接口没写好,选别的测吧');
    return;
  }

  const res = await getOrderCodesApi({
    orderType: detailForm.documentType,
  });
  Object.assign(orderCodesOptions.value, res);
};

const title = computed(() => {
  return detailForm.id ? '编辑项目' : '新增项目';
});

const init = async (data: any) => {
  const res = await projectManageListApi({ projectName: '' });
  projectInfoOptions.value = res.map((item) => ({
    projectName: item.projectName,
    id: item.id,
  }));
  await getCompanyList();
  // await getOrderCodesList(); // 不传参会报类型不能为空的错
  if (data.id) {
    const res = await detailShipmentApi(data.id);
    Object.assign(detailForm, res);
  } else {
    detailForm = defaultsDeep(defaultForm);
  }
};

const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);

const formRef = ref();
const save = async () => {
  try {
    await formRef.value.validate();

    changeOkLoading(true);

    const res = detailForm.id ? await editShipmentApi(detailForm) : await addShipmentApi(detailForm);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } catch (error) {
    message.error('保存失败，请检查网络或输入内容');
    console.error('新增仓库失败:', error);
    changeOkLoading(false); // 防止 loading 卡住
  } finally {
    changeOkLoading(false);
  }
};

const labelCol = { style: { width: '150px' } };

const gridLocation: VxeTableGridOptions = {
  data: computed(() => detailForm.inboundReceiptItemBOs),
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'productName',
      title: '运载工具类型',
      editRender: {},
      slots: { edit: 'edit_product_name' },
      minWidth: '150px',
    },
    {
      field: 'productAlias',
      title: '车牌/船名/载具号',
      editRender: {},
      slots: { edit: 'edit_product_alias' },
      minWidth: '160px',
    },
    {
      field: 'productCode',
      title: '起点位置',
      editRender: {},
      slots: { edit: 'edit_product_code' },
      minWidth: '150px',
    },
    {
      field: 'specifications',
      title: '终点位置',
      editRender: {},
      slots: { edit: 'edit_specifications' },
      minWidth: '150px',
    },
    { field: 'brandName', title: '商品品牌', editRender: {}, slots: { edit: 'edit_brand_name' }, minWidth: '150px' },
    {
      field: 'originName',
      title: '预计费用(元)',
      editRender: {},
      slots: { edit: 'edit_origin_name' },
      minWidth: '150px',
    },
    {
      field: 'measureUnit',
      title: '开始日期',
      editRender: {},
      slots: { edit: 'edit_measure_unit' },
      minWidth: '150px',
    },
    // { field: 'locationType', title: '已入库重量', editRender: {}, slots: { edit: 'edit_location_type' }, minWidth: '150px' }, //  后台反
    // { field: 'locationType', title: '未入库重量', editRender: {}, slots: { edit: 'edit_location_type' }, minWidth: '150px' }, // 源单减去已入库
    {
      field: 'quantity',
      title: '结束日期',
      editRender: {},
      slots: { edit: 'edit_quantity' },
      minWidth: '150px',
    }, // 自己填，得小于未入库
    {
      field: 'sourceDocumentItemNumber',
      title: '联系人姓名',
      editRender: {},
      slots: { edit: 'edit_source_document_item_number' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentDisplay',
      title: '联系人电话',
      editRender: {},
      slots: { edit: 'edit_source_document_display' },
      minWidth: '150px',
    },
    {
      field: 'serialNumbers',
      title: '物流单号',
      editRender: {},
      slots: { edit: 'edit_serial_numbers' },
      minWidth: '150px',
    },
    { field: 'remarks', title: '备注', editRender: {}, slots: { edit: 'edit_remarks' }, minWidth: '200px' },
  ],
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};

const gridLocation2: VxeTableGridOptions = {
  data: computed(() => detailForm.inboundReceiptItemBOs),
  columns: [
    { type: 'checkbox', width: '60px', fixed: 'left' },
    {
      field: 'productName1',
      title: '商品名称',
      editRender: {},
      slots: { edit: 'edit_product_name' },
      minWidth: '150px',
    },
    {
      field: 'productName2',
      title: '商品别名',
      editRender: {},
      slots: { edit: 'edit_product_name' },
      minWidth: '150px',
    },
    {
      field: 'productName',
      title: '规格型号',
      editRender: {},
      slots: { edit: 'edit_product_name' },
      minWidth: '150px',
    },
    {
      field: 'productAlias',
      title: '商品编码',
      editRender: {},
      slots: { edit: 'edit_product_alias' },
      minWidth: '150px',
    },
    {
      field: 'productCode',
      title: '计量单位',
      editRender: {},
      slots: { edit: 'edit_product_code' },
      minWidth: '150px',
    },
    {
      field: 'specifications',
      title: '商品品牌',
      editRender: {},
      slots: { edit: 'edit_specifications' },
      minWidth: '150px',
    },
    { field: 'brandName', title: '商品品牌', editRender: {}, slots: { edit: 'edit_brand_name' }, minWidth: '150px' },
    {
      field: 'originName',
      title: '生产厂家',
      editRender: {},
      slots: { edit: 'edit_origin_name' },
      minWidth: '150px',
    },
    {
      field: 'measureUnit',
      title: '已发运重量',
      editRender: {},
      slots: { edit: 'edit_measure_unit' },
      minWidth: '150px',
    },
    // { field: 'locationType', title: '已入库重量', editRender: {}, slots: { edit: 'edit_location_type' }, minWidth: '150px' }, //  后台反
    // { field: 'locationType', title: '未入库重量', editRender: {}, slots: { edit: 'edit_location_type' }, minWidth: '150px' }, // 源单减去已入库
    {
      field: 'quantity',
      title: '未发运重量',
      editRender: {},
      slots: { edit: 'edit_quantity' },
      minWidth: '150px',
    }, // 自己填，得小于未入库
    {
      field: 'sourceDocumentItemNumber',
      title: '本次发运重量',
      editRender: {},
      slots: { edit: 'edit_source_document_item_number' },
      minWidth: '150px',
    },
    {
      field: 'sourceDocumentDisplay',
      title: '订单商品行号',
      editRender: {},
      slots: { edit: 'edit_source_document_display' },
      minWidth: '150px',
    },
    {
      field: 'serialNumbers',
      title: '采购订单编号',
      editRender: {},
      slots: { edit: 'edit_serial_numbers' },
      minWidth: '150px',
    },
    { field: 'remarks', title: '备注', editRender: {}, slots: { edit: 'edit_remarks' }, minWidth: '200px' },
  ],
  editConfig: {
    trigger: 'click',
    mode: 'row',
  },
  toolbarConfig: {
    slots: {
      tools: 'toolbar_location_tools',
    },
  },
};

const selectedProjectCode = ref('');
const selectedExecutorCompanyName = ref('');
const handleProjectChange = (id: string) => {
  projectInfoOptions.map((item) => {
    if (item.id === id) {
      selectedProjectCode.value = item.projectCode?.toString();
      selectedExecutorCompanyName.value = item.executorCompanyName;
    }
  });
};

const handleDocumentTypeChange = async () => {
  await getOrderCodesList();
};

// 新增行
const addLocationRow = async (gridApi: GridApi) => {
  const newRecord = {
    id: defaultForm.inboundReceiptItemBOs.length,
    createBy: 0,
    createTime: '',
    updateBy: 0,
    updateTime: '',
    deleteFlag: 0,
    version: 0,
    inboundReceiptId: 0,
    sourceDocumentItemNumber: 0,
    productName: '',
    productCode: '',
    productAlias: '',
    measureUnit: '',
    specifications: '',
    originName: '',
    brandName: '',
    quantity: 0,
    warehouseId: 0,
    serialNumbers: '',
    sourceDocumentType: '',
    sourceDocumentId: 0,
    sourceDocumentDisplay: '',
    sourceDocumentItemId: 0,
    sourceDocumentItemDisplay: 0,
    remarks: '',
  };
  const $grid = gridApi.grid;
  if ($grid) {
    await $grid.insertAt(newRecord, -1);
  }
};

// 删除行
const removeLocationRow = async (gridApi: GridApi) => {
  const $grid = gridApi.grid;
  if ($grid) {
    const selectedRows = $grid.getCheckboxRecords();
    if (selectedRows.length > 0) {
      $grid.remove(selectedRows);
      message.success('删除成功');
    } else {
      message.warning('请选择要删除的数据');
    }
  }
};

// 注册表格
const [GridLocation, gridApiLocation] = useVbenVxeGrid({
  gridOptions: gridLocation,
});

const [GridLocation2, gridApiLocation2] = useVbenVxeGrid({
  gridOptions: gridLocation2,
});
</script>

<template>
  <BasicPopup v-bind="$attrs" show-ok-btn :title="title" @register="registerPopup" @ok="save">
    <div :class="BASE_PAGE_CLASS_NAME">
      <Form
        ref="formRef"
        :colon="false"
        :model="detailForm"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="{ span: 20 }"
        class="px-8"
      >
        <!-- 基本信息 -->
        <BasicCaption content="基本信息" />
        <Row class="mt-5">
          <!-- 发货单编号 -->
          <Col v-bind="colSpan">
            <FormItem label="发货单编号" name="shipmentCode">
              <span v-if="detailForm.id"> {{ detailForm.shipmentCode }} </span>
              <span v-else> - </span>
            </FormItem>
          </Col>

          <!-- 运输企业 -->
          <Col v-bind="colSpan">
            <FormItem label="运输企业" name="carrierCompanyCode">
              <Select
                v-model:value="detailForm.carrierCompanyCode"
                :options="companyOptions"
                :field-names="{ label: 'companyName', value: 'companyCode' }"
                show-search
                :filter-option="
                  (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
                "
              />
            </FormItem>
          </Col>

          <!-- 运输方式 -->
          <Col v-bind="colSpan">
            <FormItem label="运输方式" name="transportMethod">
              <Select v-model:value="detailForm.transportMethod" :options="getDictList('TRANSPORT_MODE')" />
            </FormItem>
          </Col>

          <!-- 运输类型 -->
          <Col v-bind="colSpan">
            <FormItem label="运输类型" name="shipmentType">
              <Select v-model:value="detailForm.shipmentType" :options="getDictList('TRANSPORT_TYPE')" />
            </FormItem>
          </Col>

          <!-- 所属项目名称 -->
          <Col v-bind="colSpan">
            <FormItem label="所属项目名称" name="projectName">
              <Select
                v-model:value="detailForm.projectName"
                :options="projectInfoOptions"
                show-search
                :field-names="{ label: 'projectName', value: 'id' }"
                :filter-option="(input: string, option: any) => option.projectName.includes(input)"
                @change="handleProjectChange"
              />
            </FormItem>
          </Col>

          <!-- 所属项目编号 -->
          <Col v-bind="colSpan">
            <FormItem label="所属项目编号" name="projectCode">
              <span>{{ detailForm.projectCode || selectedProjectCode || '-' }}</span>
            </FormItem>
          </Col>

          <!-- 发运日期 -->
          <Col v-bind="colSpan">
            <FormItem label="发运日期" name="shipmentDate">
              <DatePicker
                v-model:value="detailForm.shipmentDate"
                value-format="YYYY-MM-DD hh:mm:ss"
                format="YYYY-MM-DD"
              />
            </FormItem>
          </Col>

          <!-- 预计收货日期 -->
          <Col v-bind="colSpan">
            <FormItem label="预计收货日期" name="plannedDeliveryDate">
              <DatePicker
                v-model:value="detailForm.plannedDeliveryDate"
                value-format="YYYY-MM-DD hh:mm:ss"
                format="YYYY-MM-DD"
              />
            </FormItem>
          </Col>

          <!-- 关联单据类型 -->
          <Col v-bind="colSpan">
            <FormItem label="关联单据类型" name="documentType">
              <Select
                v-model:value="detailForm.documentType"
                :options="getDictList('DOCUMENT_TYPE')"
                @change="handleDocumentTypeChange"
              />
            </FormItem>
          </Col>

          <!-- 关联单据 -->
          <Col v-bind="colSpan">
            <FormItem label="关联单据" name="shipmentSourceRelList">
              <Select
                v-model:value="detailForm.shipmentSourceRelList"
                mode="multiple"
                :options="orderCodesOptions"
                show-search
                :field-names="{ label: 'projectName', value: 'id' }"
                :filter-option="(input: string, option: any) => option.projectName.includes(input)"
              />
            </FormItem>
          </Col>

          <!-- 收货企业 -->
          <Col v-bind="colSpan">
            <FormItem label="收货企业" name="consigneeCompanyCode">
              <Select
                v-model:value="detailForm.consigneeCompanyCode"
                :options="companyOptions"
                :field-names="{ label: 'companyName', value: 'companyCode' }"
                show-search
                :filter-option="
                  (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
                "
              />
            </FormItem>
          </Col>

          <!-- 结算公司	 -->
          <Col v-bind="colSpan">
            <FormItem label="结算公司" name="billingCompanyCode">
              <Select
                v-model:value="detailForm.billingCompanyCode"
                :options="companyOptions"
                :field-names="{ label: 'companyName', value: 'companyCode' }"
                show-search
                :filter-option="
                  (input: string, option: any) => option.companyName.toLowerCase().includes(input.toLowerCase())
                "
              />
            </FormItem>
          </Col>

          <!-- 贸易执行企业 -->
          <Col v-bind="colSpan">
            <FormItem label="贸易执行企业" name="executorCompanyName">
              <span> {{ detailForm.executorCompanyName || selectedExecutorCompanyName || '-' }} </span>
            </FormItem>
          </Col>

          <!-- 发货地址 -->
          <Col v-bind="colSpan">
            <Row :gutter="12">
              <Col :span="12">
                <FormItem label="发货地址" name="detailAddress">
                  <BaseRegionPicker
                    v-model:province="detailForm.province"
                    v-model:city="detailForm.city"
                    v-model:district="detailForm.district"
                    :disabled="!!detailForm.id"
                  />
                </FormItem>
              </Col>
              <Col :span="12">
                <FormItem name="detailAddress">
                  <Input v-model:value="detailForm.detailAddress" :disabled="!!detailForm.id" />
                </FormItem>
              </Col>
            </Row>
          </Col>

          <!-- 收货地址 -->
          <Col v-bind="colSpan">
            <Row :gutter="12">
              <Col :span="12">
                <FormItem label="收货地址" name="receiptDetailAddress">
                  <BaseRegionPicker
                    v-model:province="detailForm.receiptProvince"
                    v-model:city="detailForm.receiptCity"
                    v-model:district="detailForm.receiptDistrict"
                    :disabled="!!detailForm.id"
                  />
                </FormItem>
              </Col>
              <Col :span="12">
                <FormItem name="receiptDetailAddress">
                  <Input v-model:value="detailForm.receiptDetailAddress" :disabled="!!detailForm.id" />
                </FormItem>
              </Col>
            </Row>
          </Col>

          <!-- 备注 -->
          <Col v-bind="colSpan">
            <FormItem label="备注" name="remarks">
              <Textarea v-model:value="detailForm.remarks" :rows="3" />
            </FormItem>
          </Col>
        </Row>

        <!-- 物流运输信息 -->
        <BasicCaption content="物流运输信息" />
        <div>
          <GridLocation>
            <template #toolbar_location_tools>
              <Button class="mr-2" type="primary" @click="() => addLocationRow(gridApiLocation)">增行</Button>
              <Button class="mr-2" danger @click="() => removeLocationRow(gridApiLocation)">删行</Button>
            </template>

            <template #edit_product_name="{ row }">
              <Input v-model:value="row.productName" placeholder="请输入运载工具类型" />
            </template>

            <template #edit_product_alias="{ row }">
              <Input v-model:value="row.productAlias" placeholder="请输入车牌/船名/载具号" />
            </template>

            <template #edit_product_code="{ row }">
              <Input v-model:value="row.productCode" placeholder="请输入起点位置" />
            </template>

            <template #edit_specifications="{ row }">
              <Input v-model:value="row.specifications" placeholder="请输入终点位置" />
            </template>

            <template #edit_brand_name="{ row }">
              <Input v-model:value="row.brandName" placeholder="请输入预计费用(元)" />
            </template>

            <template #edit_origin_name="{ row }">
              <Input v-model:value="row.originName" placeholder="请输入开始日期" />
            </template>

            <template #edit_measure_unit="{ row }">
              <Input v-model:value="row.measureUnit" placeholder="请输入结束日期" />
            </template>

            <template #edit_quantity="{ row }">
              <Input v-model:value="row.quantity" placeholder="请输入联系人姓名" />
            </template>

            <template #edit_source_document_item_number="{ row }">
              <Input v-model:value="row.sourceDocumentItemNumber" placeholder="请输入联系人电话" />
            </template>

            <template #edit_source_document_display="{ row }">
              <Input v-model:value="row.sourceDocumentDisplay" placeholder="请输入物流单号" />
            </template>

            <template #edit_remarks="{ row }">
              <Input v-model:value="row.remarks" placeholder="请输入备注" />
            </template>
          </GridLocation>
        </div>

        <!-- 商品信息 -->
        <BasicCaption content="商品信息" />
        <div>
          <GridLocation2>
            <template #toolbar_location_tools>
              <Button class="mr-2" type="primary" @click="() => addLocationRow(gridApiLocation2)">增行</Button>
              <Button class="mr-2" danger @click="() => removeLocationRow(gridApiLocation2)">删行</Button>
            </template>

            <template #edit_product_name="{ row }">
              <Input v-model:value="row.productName" placeholder="请输入商品名称" />
            </template>

            <template #edit_product_alias="{ row }">
              <Input v-model:value="row.productAlias" placeholder="请输入商品别名" />
            </template>

            <template #edit_product_code="{ row }">
              <Input v-model:value="row.productCode" placeholder="请输入规格型号" />
            </template>

            <template #edit_specifications="{ row }">
              <Input v-model:value="row.specifications" placeholder="请输入商品编码" />
            </template>

            <template #edit_brand_name="{ row }">
              <Input v-model:value="row.brandName" placeholder="请输入计量单位" />
            </template>

            <template #edit_origin_name="{ row }">
              <Input v-model:value="row.originName" placeholder="请输入商品品牌" />
            </template>

            <template #edit_measure_unit="{ row }">
              <Input v-model:value="row.measureUnit" placeholder="请输入生产厂家" />
            </template>

            <template #edit_quantity="{ row }">
              <Input v-model:value="row.quantity" placeholder="请输入已发运重量" />
            </template>

            <template #edit_source_document_item_number="{ row }">
              <Input v-model:value="row.sourceDocumentItemNumber" placeholder="请输入未发运重量" />
            </template>

            <template #edit_source_document_display="{ row }">
              <Input v-model:value="row.sourceDocumentDisplay" placeholder="请输入本次发运重量" />
            </template>

            <template #edit_source_document_display1="{ row }">
              <Input v-model:value="row.sourceDocumentDisplay" placeholder="请输入订单商品行号" />
            </template>

            <template #edit_source_document_display2="{ row }">
              <Input v-model:value="row.sourceDocumentDisplay" placeholder="请输入采购订单编号" />
            </template>

            <template #edit_remarks="{ row }">
              <Input v-model:value="row.remarks" placeholder="请输入备注" />
            </template>
          </GridLocation2>
        </div>

        <!-- 附件信息 -->
        <BaseAttachmentList
          v-model="detailForm.attachmentList"
          :business-id="detailForm.id"
          business-type="SCM_SHIPMENT"
          :edit-mode="!detailForm.id"
        />
      </Form>
    </div>
  </BasicPopup>
</template>

<style scoped>
:deep(.ant-picker) {
  width: 100%;
}

:deep(.fe-basic-caption-border) {
  display: none;
}
</style>
