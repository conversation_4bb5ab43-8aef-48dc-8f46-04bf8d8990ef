<script setup lang="ts">
import { onMounted, reactive, ref } from 'vue';
import type { VbenFormProps } from '@vben/common-ui';
import type { VxeTableGridOptions } from '@vben/plugins/vxe-table';
import { Page } from '@vben/common-ui';
import { FeUserSelect, usePopup } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { defineFormOptions } from '@vben/utils';

import {
  type ProjectBaseInfo,
  type ProjectTransferInfo,
  projectManagePageApi,
  projectManageTransferApi,
  getCompanyApi,
  getUserListApi,
  getUserInfoByIdsApi,
  getUserListByKeywordApi,
  getUserTreeListApi,
  projectManageCompleteApi,
} from '#/api';
import { useVbenVxeGrid } from '#/adapter/vxe-table';
import type { Rule } from 'ant-design-vue/es/form';
import { Form, FormItem, message, Modal, Space, Spin, TypographyLink, Modal as AntdModal } from 'ant-design-vue';

import Change from './change.vue';
import Detail from '../components/detail.vue';

const { getDictList } = useDictStore();
const labelCol = { style: { width: '150px' } };
const sortKey = ref<string>('create_time');
const dataLoaded = ref(false); // 添加加载状态
const usersOptions = ref([]);
const companyOptions = ref([]);
// 获取用户列表
const getUserList = async () => {
  const res = await getUserListApi();
  Object.assign(usersOptions.value, res);
};
// 获取企业列表
const getCompanyList = async () => {
  const res = await getCompanyApi();
  Object.assign(companyOptions.value, res);
};
// 同时执行两个异步请求
const loadData = async () => {
  try {
    await Promise.all([getCompanyList(), getUserList()]);
  } finally {
    dataLoaded.value = true;
  }
};

const transferForm = reactive<ProjectTransferInfo>({
  id: undefined,
  businessManagerId: [],
  operationManagerId: [],
  financeManagerId: [],
  riskManagerId: [],
});

// 根据接口必填字段定义验证规则
const rules: Record<string, Rule[]> = {
  businessManagerId: [{ required: true, message: '请选择业务负责人', trigger: 'change', type: 'array' }],
  operationManagerId: [{ required: true, message: '请选择经营负责人', trigger: 'change', type: 'array' }],
  financeManagerId: [{ required: true, message: '请选择财务负责人', trigger: 'change', type: 'array' }],
  riskManagerId: [{ required: true, message: '请选择风控负责人', trigger: 'change', type: 'array' }],
};

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'projectCode',
      label: '项目编号',
    },
    {
      component: 'Input',
      fieldName: 'projectName',
      label: '项目名称',
    },
    {
      component: 'Select',
      fieldName: 'executorCompanyName',
      label: '贸易执行企业',
      componentProps: {
        options: companyOptions.value,
        fieldNames: { label: 'companyName', value: 'companyCode' },
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'businessStructure',
      label: '业务结构',
      componentProps: {
        options: getDictList('BUS_STRUCTURE'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'projectModel',
      label: '项目模式',
      componentProps: {
        options: getDictList('PROJECT_MODE'),
        allowClear: true,
      },
    },
    // {
    //   component: 'Select',
    //   fieldName: 'projectReviewStatus',
    //   label: '评审状态',
    //   componentProps: {
    //     options: getDictList('PROJECT_REVIEW_STATUS'),
    //     allowClear: true,
    //   },
    // },
    {
      component: 'Select',
      fieldName: 'approvalStatus',
      label: '审批状态',
      componentProps: {
        options: getDictList('REVIEW_STATUS'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'uploadStatus',
      label: '会议纪要状态',
      componentProps: {
        options: getDictList('PROJECT_MEETING_STATUS'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '项目状态',
      componentProps: {
        options: getDictList('PROJECT_STATUS'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'purchaseMode',
      label: '采购模式',
      componentProps: {
        options: getDictList('PURCHASE_MODE'),
        allowClear: true,
      },
    },
    {
      component: 'Select',
      fieldName: 'changeStatus',
      label: '变更评审状态',
      componentProps: {
        options: getDictList('PROJECT_REVIEW_STATUS'),
        allowClear: true,
      },
    },
  ],
  showCollapseButton: true,
  // 按下回车时是否提交表单
  submitOnEnter: true,
});

const gridOptions: VxeTableGridOptions = {
  columns: [
    { field: 'projectCode', title: '项目编号' },
    { field: 'projectName', title: '项目名称' },
    { field: 'businessStructure', title: '业务结构', formatter: ['formatStatus', 'BUS_STRUCTURE'] },
    { field: 'projectModel', title: '项目模式', formatter: ['formatStatus', 'PROJECT_MODE'] },
    { field: 'executorCompanyName', title: '贸易执行企业' },
    {
      field: 'purchaseMode',
      title: '采购模式',
      formatter: ({ cellValue }) => {
        if (!cellValue) return '';

        // 返回字符串，按逗号分割
        if (typeof cellValue === 'string') {
          const values = cellValue.split(',').filter((item) => item.trim() !== '');
          const dictList = getDictList('PURCHASE_MODE');

          return values
            .map((value) => {
              const dictItem = dictList.find((item) => item.value === value);
              return dictItem ? dictItem.label : value;
            })
            .join(', ');
        }
        return cellValue;
      },
    },
    // {
    //   field: 'projectReviewStatus',
    //   title: '评审状态',
    //   formatter: ['formatStatus', 'PROJECT_REVIEW_STATUS'],
    // },
    {
      field: 'approvalStatus',
      title: '审批状态',
      formatter: ['formatStatus', 'REVIEW_STATUS'],
    },
    {
      field: 'status',
      title: '项目状态',
      formatter: ['formatStatus', 'PROJECT_STATUS'],
    },
    // {
    //   field: 'reviewType',
    //   title: '评审类型',
    //   formatter: ['formatStatus', 'PROJECT_REVIEW_TYPE'],
    // },
    {
      field: 'changeStatus',
      title: '变更评审状态',
      formatter: ['formatStatus', 'PROJECT_REVIEW_STATUS'],
    },
    {
      field: 'businessManager',
      title: '业务负责人',
      formatter: (managers) => {
        if (!managers.cellValue || !Array.isArray(managers.cellValue) || managers.cellValue.length === 0) {
          return '';
        }
        return managers.cellValue
          .map((manager) => manager.userName)
          .filter((name) => name)
          .join(', ');
      },
    },
    { field: 'planStartDate', title: '预计开始日期', formatter: 'formatDate' },
    { field: 'createName', title: '创建人' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 200,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        const processedFormValues = { ...formValues };

        // 如果 purchaseMode 是数组，则转换为逗号分隔的字符串
        // if (Array.isArray(processedFormValues.purchaseMode)) {
        //   processedFormValues.purchaseMode = processedFormValues.purchaseMode.join(',');
        // }

        return await projectManagePageApi({
          current: page.currentPage,
          size: page.pageSize,
          descs: sortKey.value,
          ...processedFormValues,
        });
      },
    },
  },
  toolbarConfig: {
    custom: true,
    refresh: true,
    resizable: true,
    zoom: true,
  },
};

const manageTransferModal = reactive({
  visible: false,
});

const handleTransferOk = async () => {
  try {
    await projectManageTransferApi(transferForm);
    manageTransferModal.visible = false;
    await gridApi.formApi.submitForm();
    message.success($t('base.resSuccess'));
  } catch (error: any) {
    message.error(error.message || $t('base.resFail'));
  }
};

const handleTransferCancel = () => {
  manageTransferModal.visible = false;
};

const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});

const [registerDetailForm, { openPopup: openDetailPopup }] = usePopup();
const [registerChangeForm, { openPopup: openChangePopup }] = usePopup();

const projectType = 'info';
const detail = (row: ProjectBaseInfo) => {
  const detailRow = { ...row, projectType: projectType };
  openDetailPopup(true, detailRow);
};

const change = async (row: ProjectBaseInfo) => {
  openChangePopup(true, row);
};

const complete = async (row: ProjectBaseInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmCompleteTitle'),
    content: $t('base.confirmCompleteContent'),
    async onOk() {
      try {
        const submitRow = {
          id: row.id,
          status: 'COMPLETED',
        };
        await projectManageCompleteApi(submitRow);
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } catch (error: any) {
        message.error('失败: ' + error.message);
      }
    },
  });
};

const cancelComplete = async (row: ProjectBaseInfo) => {
  AntdModal.confirm({
    title: $t('base.confirmCancelCompleteTitle'),
    content: $t('base.confirmCancelCompleteContent'),
    async onOk() {
      try {
        const submitRow = {
          id: row.id,
          status: 'EFFECTIVE',
        };
        await projectManageCompleteApi(submitRow);
        message.success($t('base.resSuccess'));
        await gridApi.formApi.submitForm();
      } catch (error: any) {
        message.error('失败: ' + error.message);
      }
    },
  });
};

const detailSuccess = () => {
  gridApi.formApi.submitForm();
};

const changeSuccess = () => {
  gridApi.formApi.submitForm();
};

const transfer = async (row: ProjectBaseInfo) => {
  manageTransferModal.visible = true;
  Object.assign(transferForm, {
    id: row.id,
    businessManagerId: row.businessManagerId ?? [],
    operationManagerId: row.operationManagerId ?? [],
    financeManagerId: row.financeManagerId ?? [],
    riskManagerId: row.riskManagerId ?? [],
  });
};

onMounted(() => {
  loadData();
});
</script>

<template>
  <Page auto-content-height>
    <Grid v-if="dataLoaded">
      <template #action="{ row }">
        <Space>
          <TypographyLink @click="detail(row)">
            {{ $t('base.detail') }}
          </TypographyLink>
          <TypographyLink
            v-if="row.changeStatus !== 'UNDER_REVIEW' && row.status !== 'COMPLETED'"
            @click="transfer(row)"
          >
            {{ $t('base.transfer') }}
          </TypographyLink>
          <TypographyLink v-if="row.changeStatus !== 'UNDER_REVIEW' && row.status !== 'COMPLETED'" @click="change(row)">
            {{ $t('base.change') }}
          </TypographyLink>
          <TypographyLink v-if="row.status === 'COMPLETED'" @click="cancelComplete(row)">
            {{ $t('base.cancel') }}
          </TypographyLink>
          <TypographyLink
            v-if="
              row.status === 'EFFECTIVE' &&
              (row.changeStatus === 'NOT_CHANGE' ||
                row.changeStatus === 'REVIEWED' ||
                row.changeStatus === 'NOT_REVIEWED')
            "
            @click="complete(row)"
          >
            {{ $t('base.completed') }}
          </TypographyLink>
        </Space>
      </template>
    </Grid>
    <div v-else class="flex h-64 items-center justify-center">
      <Spin size="large" />
    </div>
    <Detail @register="registerDetailForm" @ok="detailSuccess" />
    <Change @register="registerChangeForm" @ok="changeSuccess" />

    <Modal
      v-model:open="manageTransferModal.visible"
      title="移交项目"
      @ok="handleTransferOk"
      @cancel="handleTransferCancel"
      width="800px"
    >
      <Form
        :colon="false"
        :model="transferForm"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="{ span: 20 }"
        class="px-8"
      >
        <FormItem label="业务负责人" name="businessManagerId">
          <FeUserSelect
            v-model:value="transferForm.businessManagerId"
            multiple
            :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
          />
        </FormItem>
        <FormItem label="运营负责人" name="operationManagerId">
          <FeUserSelect
            v-model:value="transferForm.operationManagerId"
            multiple
            :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
          />
        </FormItem>
        <FormItem label="财务负责人" name="financeManagerId">
          <FeUserSelect
            v-model:value="transferForm.financeManagerId"
            multiple
            :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
          />
        </FormItem>
        <FormItem label="风控负责人" name="riskManagerId">
          <FeUserSelect
            v-model:value="transferForm.riskManagerId"
            multiple
            :api="{ getUserInfoByIdsApi, getUserListByKeywordApi, getUserTreeListApi, getUserListApi }"
          />
        </FormItem>
      </Form>
    </Modal>
  </Page>
</template>

<style></style>
