<script setup lang="ts">
import type { Rule } from 'ant-design-vue/es/form';

import type { ABSManageInfo } from '#/api/abs-manage';

import { computed, ref } from 'vue';

import { ApiComponent } from '@vben/common-ui';
import { BasicPopup, usePopupInner } from '@vben/fe-ui';
import { $t } from '@vben/locales';
import { useDictStore } from '@vben/stores';
import { formatDate } from '@vben/utils';

import { message, Select } from 'ant-design-vue';
import dayjs from 'dayjs';
import { cloneDeep } from 'lodash-es';

import { BaseAttachmentList } from '#/adapter/base-ui';
import { getProjectListApi } from '#/api';
import { addABSApi, detailABSApi, editABSApi, submitABSApi } from '#/api/abs-manage';

const emit = defineEmits(['register', 'ok']);
const colSpan = { md: 12, sm: 24 };
const dictStore = useDictStore();

const absInfoForm = ref<ABSManageInfo>({});
const formRef = ref();
const rules: Record<string, Rule[]> = {
  absProjectName: [{ required: true, message: '请输入ABS名称', trigger: 'blur' }],
  projectName: [{ required: true, message: '请选择项目', trigger: 'blur' }],
};
const title = computed(() => {
  return absInfoForm.value.id ? '编辑ABS项目' : '新增ABS项目';
});
const init = async (data: ABSManageInfo) => {
  if (data.id) {
    absInfoForm.value = await detailABSApi(data.id);
    absInfoForm.value.projectDate = formatDate(absInfoForm.value.projectDate || '', 'YYYY-MM-DD HH:mm:ss') as string;
  } else {
    absInfoForm.value = data;
    absInfoForm.value.projectDate = dayjs().format('YYYY-MM-DD HH:mm:ss');
  }
};
const save = async (type: string) => {
  await formRef.value.validate();
  const formData = cloneDeep(absInfoForm.value);
  formData.status = type;
  formData.projectId = Number(formData.projectId);
  formData.id = absInfoForm.value.id || null;
  changeOkLoading(true);
  let api = type === 'SUBMITTED' ? submitABSApi : addABSApi;
  if (absInfoForm.value.id && type === 'DRAFTING') {
    api = editABSApi;
  }
  try {
    const res = await api(formData as ABSManageInfo);
    message.success($t('base.resSuccess'));
    emit('ok', res);
    closePopup();
  } finally {
    changeOkLoading(false);
  }
};
const [registerPopup, { changeOkLoading, closePopup }] = usePopupInner(init);
</script>

<template>
  <BasicPopup v-bind="$attrs" :title="title" @register="registerPopup">
    <template #insertToolbar>
      <a-space>
        <a-button type="primary" @click="save('DRAFTING')">保存</a-button>
        <a-button type="primary" @click="save('SUBMITTED')">提交</a-button>
      </a-space>
    </template>
    <a-form
      ref="formRef"
      :colon="false"
      :model="absInfoForm"
      :rules="rules"
      :label-col="{ style: { width: '150px' } }"
      :wrapper-col="{ span: 20 }"
      class="px-8"
    >
      <a-row class="mt-5">
        <a-col v-bind="colSpan">
          <a-form-item label="ABS项目编号" name="absProjectCode">
            <a-input v-model:value="absInfoForm.absProjectCode" disabled v-if="absInfoForm.id" />
            <a-input value="自动生成" disabled v-else />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="ABS名称" name="absProjectName">
            <a-input v-model:value="absInfoForm.absProjectName" />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="项目名称" name="projectName">
            <ApiComponent
              v-model="absInfoForm.projectName"
              :component="Select"
              :api="getProjectListApi"
              label-field="projectName"
              value-field="id"
              model-prop-name="value"
              show-search
              :filter-option="(input: string, option: any) => option.label.includes(input)"
              @change="
                (_value: string, option: any) => {
                  absInfoForm.projectId = option.value;
                  absInfoForm.projectName = option.label;
                }
              "
            />
          </a-form-item>
        </a-col>
        <a-col v-bind="colSpan">
          <a-form-item label="日期" name="projectDate">
            <a-date-picker v-model:value="absInfoForm.projectDate" value-format="YYYY-MM-DD HH:mm:ss" class="w-full" />
          </a-form-item>
        </a-col>
        <a-col :span="24">
          <a-form-item label="备注" name="remarks">
            <a-textarea
              v-model:value="absInfoForm.remark"
              :rows="4"
              placeholder="进行简要描述，包括项目背景、目的、预期效果等。"
            />
          </a-form-item>
        </a-col>
      </a-row>
      <BaseAttachmentList
        :key="absInfoForm.id"
        v-model="absInfoForm.attachmentList"
        :business-id="absInfoForm.id"
        business-type="SCM_ABS"
        edit-mode
      />
    </a-form>
  </BasicPopup>
</template>
