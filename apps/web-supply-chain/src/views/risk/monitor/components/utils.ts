export const formatDictValue = (options: { label: string; value: number | string }[]) => {
  return function (value: string) {
    const option = options.find((item) => item.value === value);
    return option?.label ?? value;
  };
};
/**
 * 深度遍历并清理对象/数组，可方便地扩展清理规则。
 * @param data 需要处理的数据
 * @returns {any} 清理后的数据
 */
export const deepClean = (data: any): any => {
  /**
   * 1. 定义集中的字符串处理函数
   * 所有针对字符串的清理规则都应在此处添加。
   * @param str 待处理的字符串
   */
  const processString = (str: string): string => {
    return str.trim();
  };

  /**
   * 2. 递归遍历函数
   * @param currentData 当前遍历到的数据
   */
  const traverse = (currentData: any): any => {
    // 基础情况：如果不是对象或为 null，则进行处理
    if (currentData === null || typeof currentData !== 'object') {
      // 如果是字符串，则使用集中的处理函数
      return typeof currentData === 'string' ? processString(currentData) : currentData;
    }

    // 数组情况：递归遍历每一项
    if (Array.isArray(currentData)) {
      return currentData.map((item) => traverse(item));
    }

    // 对象情况：遍历键值对
    const cleanedObject: { [key: string]: any } = {};
    for (const [key, value] of Object.entries(currentData)) {
      // 使用集中的处理函数处理 key
      const processedKey = processString(key);
      // 递归处理 value
      cleanedObject[processedKey] = traverse(value);
    }
    return cleanedObject;
  };

  // 启动遍历过程
  return traverse(data);
};
