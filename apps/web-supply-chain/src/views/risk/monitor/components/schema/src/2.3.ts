import type { SchemaItem } from '../index';

export const name: string = '股权出质';
export const schema: SchemaItem[] = [
  { fieldName: 'number', label: '登记编号' },
  { fieldName: 'date', label: '股权出质设立登记日期' },
  { fieldName: 'pledgor', label: '出质人' },
  { fieldName: 'pawnee', label: '质权人' },
  { fieldName: 'pledgor_amount', label: '出质股权数额' },
  { fieldName: 'pledgor_unit', label: '出质股权数额货币单位' },
  { fieldName: 'pledgor_currency', label: '出质股权数额币种' },
  { fieldName: 'status', label: '状态' },
  { fieldName: 'remark', label: '备注' },
  { fieldName: 'cancel_date', label: '注销日期' },
  { fieldName: 'cancel_content', label: '注销原因' },
  { fieldName: 'public_date', label: '公示日期' },
  { fieldName: 'object_company', label: '标的方' },
  {
    fieldName: 'change_items',
    label: '变更信息',
    schema: [
      { fieldName: 'change_content', label: '变更内容' },
      { fieldName: 'seq_no', label: '序列号' },
      { fieldName: 'change_date', label: '变更时间' },
    ],
  },
  {
    fieldName: 'cancel_items',
    label: '股权出质注销信息',
    schema: [
      { fieldName: 'cancel_date', label: '注销日期' },
      { fieldName: 'cancel_content', label: '注销原因' },
      { fieldName: 'seq_no', label: '序号' },
    ],
  },
];

export const handleKeys = {
  change_items: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
  cancel_items: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
