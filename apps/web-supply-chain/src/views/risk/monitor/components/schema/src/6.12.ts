import type { SchemaItem } from '../index';

export const name = '排污限期整改';
export const schema: SchemaItem[] = [
  { fieldName: 'ename', label: '单位名称' },
  { fieldName: 'area', label: '区域' },
  { fieldName: 'register_no', label: '限期整改编号' },
  { fieldName: 'address', label: '地址' },
  { fieldName: 'industry', label: '行业类别' },
  { fieldName: 'valid_date', label: '有效期限' },
  { fieldName: 'issue_date', label: '下达时间' },
  { fieldName: 'attachment', label: '附件通知书' },
  {
    fieldName: 'notice_version_record',
    label: '整改通知书版本记录',
    schema: [
      { fieldName: 'notice_no', label: '通知书编号' },
      { fieldName: 'type', label: '类型' },
      { fieldName: 'version', label: '版本' },
      { fieldName: 'end_date', label: '结束日期' },
      { fieldName: 'valid_period', label: '有效期限' },
    ],
  },
  {
    fieldName: 'detail',
    label: '整改详情',
    schema: [
      { fieldName: 'num', label: '序号' },
      { fieldName: 'problem', label: '问题' },
      { fieldName: 'way', label: '整改方式' },
      { fieldName: 'date', label: '整改期限' },
      { fieldName: 'plan', label: '整改计划' },
      { fieldName: 'is_complete', label: '是否完成' },
    ],
  },
];

export const handleKeys = {
  notice_version_record: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
  detail: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
