import type { SchemaItem } from '../index';

export const name: string = '科技型中小企业';
export const schema: SchemaItem[] = [
  { fieldName: 'tech_code', label: '入库登记编号' },
  { fieldName: 'region', label: '区域' },
  { fieldName: 'year', label: '年份' },
  { fieldName: 'publish_date', label: '发布日期' },
  { fieldName: 'valid_start', label: '入库登记日期' },
  { fieldName: 'valid_end', label: '有效期至' },
  { fieldName: 'end_date', label: '撤销日期' },
  { fieldName: 'end_type', label: '撤销方式' },
  { fieldName: 'state', label: '许可证状态' },
];
