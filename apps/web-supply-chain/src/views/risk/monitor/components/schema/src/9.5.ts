import type { SchemaItem } from '../index';

export const name: string = '股权质押';
export const schema: SchemaItem[] = [
  { fieldName: 'frozprogres', label: '质押冻结进度', isChange: true },
  { fieldName: 'sharehdname', label: '股东名称' },
  { fieldName: 'sharetype', label: '质押股份类型' },
  { fieldName: 'frozenstartdate', label: '质押/冻结起始日' },
  { fieldName: 'freedate', label: '质押/冻结截止日' },
  { fieldName: 'frozentype', label: '质押冻结类型' },
  { fieldName: 'invamount', label: '涉及金额（万元）' },
  { fieldName: 'sharehdnum', label: '持股数（万股）' },
  { fieldName: 'sharefrozennum', label: '质押/冻结股数（万股）' },
  { fieldName: 'amtsharefrozen', label: '累计质押股数（万股）' },
  { fieldName: 'relinfocode', label: '公告编码' },
  { fieldName: 'updatedate', label: '资料更新时间' },
  { fieldName: 'emorderid', label: '事件id' },
  { fieldName: 'noticedate', label: '公告日期' },
  { fieldName: 'isnew', label: '是否最新' },
  { fieldName: 'rellockstate', label: '是否锁定' },
  { fieldName: 'ghdate', label: '过户日期' },
  { fieldName: 'freedtaeinad', label: '实际解除日期' },
  { fieldName: 'frozenname', label: '实施质押冻结机构' },
  { fieldName: 'achgshare', label: '变动后股数（万股）' },
  { fieldName: 'noreleasnum', label: '剩余未解押数（万股）' },
  { fieldName: 'istbreakunwindl', label: '是否触及/跌破平仓线' },
  { fieldName: 'frozenratio', label: '质押/冻结占所持股比例' },
  { fieldName: 'achgratio', label: '变动后股数占总股本比例' },
  { fieldName: 'amtsharerepo', label: '累计质押/约定回购股数' },
  { fieldName: 'amtshratio', label: '累计质押数占所持股比例' },
  { fieldName: 'amtfrozenratio', label: '累计质押股数占总股本比例' },
  { fieldName: 'amtreporatio', label: '累计质押/约定回购股数占总股本比例' },
  { fieldName: 'ndateamtsharerepo', label: '截止公告日累计质押数（万股）' },
  { fieldName: 'ndateamtshatoeqra', label: '截止公告日累计质押数占总股本比例' },
  { fieldName: 'ndateamtshratio', label: '截止公告日累计质押数占所持股比例' },
  { fieldName: 'frozendateexplain', label: '质押/冻结期限描述' },
  { fieldName: 'pledgepur', label: '质押目的' },
  { fieldName: 'meunfrpled', label: '解除质押冻结方式' },
  { fieldName: 'frozenreason', label: '质押/冻结事由' },
];
