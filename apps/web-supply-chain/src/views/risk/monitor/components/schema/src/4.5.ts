import type { SchemaItem } from '../index';

import { formatDictValue } from '../../utils';

const listsTypeDictList = [
  { label: '政府黑名单', value: '01' },
  { label: '环保失信黑名单', value: '02' },
  { label: '应急管理部安全生产失信黑名单', value: '03' },
  { label: '政府采购黑名单', value: '04' },
  { label: '“屡禁不止、屡罚不改”黑名单', value: '05' },
  { label: '其他', value: '06' },
  { label: '假冒国企名单', value: '07' },
  { label: '军队采购失信名单', value: '08' },
  { label: '政府采购失信名单', value: '09' },
  { label: '供应商暂停名单', value: '10' },
  { label: '社会组织活动异常名录', value: '11' },
  { label: '社会组织严重违法失信名单', value: '12' },
  { label: '涉金融领域黑名单', value: '13' },
];
const departmentLevelDictList = [
  { label: '国家级', value: '01' },
  { label: '省级', value: '02' },
  { label: '市级', value: '03' },
  { label: '县级', value: '04' },
  { label: '乡级', value: '05' },
  { label: '其他', value: '06' },
];

export const name: string = '黑名单移入';
export const schema: SchemaItem[] = [
  { fieldName: 'in_lists_date', label: '列入黑名单日期', isChange: true },
  { fieldName: 'lists_type', label: '黑名单类型', formatter: formatDictValue(listsTypeDictList) },
  { fieldName: 'department_level', label: '认定部门等级', formatter: formatDictValue(departmentLevelDictList) },
  { fieldName: 'credit_no', label: '统一社会信用代码' },
  { fieldName: 'black_basis', label: '黑名单认定依据', span: 2 },
  { fieldName: 'maintain_department', label: '认定部门' },
  { fieldName: 'out_lists_date', label: '移出黑名单日期' },
  { fieldName: 'details', label: '失信情况', span: 2 },
  { fieldName: 'punishment_result', label: '处罚结果' },
];
