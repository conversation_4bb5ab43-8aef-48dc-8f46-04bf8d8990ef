import type { SchemaItem } from '../index';

export const name: string = '债务人信息';
export const schema: SchemaItem[] = [
  { fieldName: 'name', label: '债务人名称' },
  { fieldName: 'industry', label: '行业' },
  { fieldName: 'region', label: '注册地' },
  { fieldName: 'assets', label: '主要资产', span: 2 },
  { fieldName: 'employees', label: '职工人数' },
  { fieldName: 'mechanism', label: '管理人机构' },
  { fieldName: 'principal_name', label: '管理人' },
];
