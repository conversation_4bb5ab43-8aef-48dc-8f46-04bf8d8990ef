import type { SchemaItem } from '../index';

export const name: string = '邮箱变更';
export const schema: SchemaItem[] = [
  { fieldName: 'business_email', label: '最新邮箱', isChange: true },
  { fieldName: 'name', label: '企业名称' },
  { fieldName: 'short_names', label: '企业简称' },
  { fieldName: 'fenname', label: '英文全称' },
  { fieldName: 'senname', label: '英文简称' },
  { fieldName: 'brief', label: '企业简介' },
  { fieldName: 'group_name', label: '所属集团' },
  { fieldName: 'website', label: '企业官网' },
  { fieldName: 'register_address', label: '注册地址' },
  { fieldName: 'business_address', label: '经营地址' },
  { fieldName: 'create_time', label: '创建时间' },
  { fieldName: 'business_telephone', label: '最新电话' },
];
