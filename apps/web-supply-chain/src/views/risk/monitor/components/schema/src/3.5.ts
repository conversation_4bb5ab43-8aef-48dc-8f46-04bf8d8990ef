import type { SchemaItem } from '../index';

import { typeDictList } from '../../constant';
import { formatDictValue } from '../../utils';

export const name: string = '司法拍卖';
export const schema: SchemaItem[] = [
  { fieldName: 'full_name', label: '拍品标的', span: 2, isChange: true },
  { fieldName: '_restrict', label: '权利限制情况及瑕疵情况' },
  { fieldName: 'basis', label: '权利来源' },
  { fieldName: 'court', label: '法院' },
  { fieldName: 'date', label: '拍卖日期' },
  { fieldName: 'description', label: '拍品介绍', span: 2 },
  { fieldName: 'name', label: '项目名称' },
  { fieldName: 'start_price', label: '起拍价' },
  { fieldName: 'owner', label: '拍品所有人' },
  { fieldName: 'url', label: '拍卖信息链接' },
  { fieldName: 'transaction_date', label: '交易日期' },
  { fieldName: 'ename', label: '企业名称' },
  { fieldName: 'content', label: '交易内容', span: 2 },
  { fieldName: 'est_price', label: '评估价' },
  { fieldName: 'type', label: '类型' },
  { fieldName: 'transaction_price', label: '交易价格' },
  { fieldName: 'certificate', label: '权证情况' },
  { fieldName: 'document', label: '提供的文件' },
  {
    fieldName: 'related_companies',
    label: '关联企业、个人',
    schema: [
      { fieldName: 'name', label: '相关方名称' },
      { fieldName: 'role', label: '相关角色', formatter: formatDictValue(typeDictList) },
    ],
  },
];

export const handleKeys = {
  related_companies: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
