import type { SchemaItem } from '../index';

import { typeDictList } from '../../constant';
import { formatDictValue } from '../../utils';

export const name = '商标转让';
export const schema: SchemaItem[] = [
  { fieldName: 'role', label: '角色' },
  { fieldName: 'reg_number', label: '申请/注册号' },
  { fieldName: 'type_num', label: '国际分类' },
  { fieldName: 'notice_no', label: '公告期号' },
  { fieldName: 'notice_type', label: '公告类型' },
  { fieldName: 'notice_date', label: '公告日期' },
  { fieldName: 'notice_content', label: '公告内容' },
  { fieldName: 'trademark_name', label: '商标名称' },
  {
    fieldName: 'assignee',
    label: '商标受让人',
    schema: [
      { fieldName: 'name', label: '名称' },
      { fieldName: 'type', label: '类型', formatter: formatDictValue(typeDictList) },
    ],
  },
  {
    fieldName: 'transferor',
    label: '商标转让人',
    schema: [
      { fieldName: 'name', label: '名称' },
      { fieldName: 'type', label: '类型', formatter: formatDictValue(typeDictList) },
    ],
  },
];

export const handleKeys = {
  assignee: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
  transferor: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
