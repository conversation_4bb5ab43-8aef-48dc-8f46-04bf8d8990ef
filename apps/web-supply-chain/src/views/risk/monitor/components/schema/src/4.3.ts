import type { SchemaItem } from '../index';

import { sexDictList } from '../../constant';
import { formatDictValue } from '../../utils';

export const name: string = '限制高消费人员';
export const schema: SchemaItem[] = [
  { fieldName: 'name', label: '被限制消费人姓名', isChange: true },
  { fieldName: 'case_no', label: '案号' },
  { fieldName: 'case_reason', label: '案由' },
  { fieldName: 'company_name', label: '关联企业名称' },
  { fieldName: 'court', label: '执行法院' },
  { fieldName: 'execution_applicant', label: '申请执行人' },
  { fieldName: 'filing_date', label: '立案时间' },
  { fieldName: 'pdf_path', label: '原文pdf链接' },
  { fieldName: 'release_date', label: '限制令发布日期' },
  { fieldName: 'sex', label: '性别', formatter: formatDictValue(sexDictList) },
  { fieldName: 'content', label: '原文全文' },
];
