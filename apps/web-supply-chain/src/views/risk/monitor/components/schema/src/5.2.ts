import type { SchemaItem } from '../index';

import { formatDictValue } from '../../utils';

const roleDictList = [
  { label: '建设单位', value: '10' },
  { label: '代理单位', value: '30' },
  { label: '中标单位', value: '40' },
  { label: '中标候选人', value: '50' },
  { label: '其他相关企业', value: '51' },
  { label: '投标单位', value: '20' },
];
const noticeTypeDictList = [
  { label: '拟建公告', value: '10' },
  { label: '招标公告', value: '20' },
  { label: '中标公告', value: '30' },
];

export const name: string = '招投标';
export const schema: SchemaItem[] = [
  { fieldName: 'date', label: '发布时间' },
  { fieldName: 'role', label: '角色', formatter: formatDictValue(roleDictList) },
  { fieldName: 'title', label: '标题', span: 2 },
  { fieldName: 'notice_type', label: '公告类型', formatter: formatDictValue(noticeTypeDictList) },
  { fieldName: 'proj_num', label: '工程号/采购编号' },
  { fieldName: 'project_bid_money', label: '项目金额（万元）' },
  { fieldName: 'industry_code', label: '行业代码' },
];
