import type { SchemaItem } from '../index';

import { typeDictList } from '../../constant';
import { formatDictValue } from '../../utils';

export const name = '担保信息';
export const schema: SchemaItem[] = [
  { fieldName: 'notice_date', label: '公告日期' },
  { fieldName: 'guar_method', label: '担保方式' },
  { fieldName: 'guar_jine', label: '担保金额（万元）' },
  {
    fieldName: 'related_companies',
    label: '关联企业',
    schema: [
      { fieldName: 'name', label: '企业名称' },
      { fieldName: 'type', label: '类型', formatter: formatDictValue(typeDictList) },
    ],
  },
];

export const handleKeys = {
  related_companies: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
