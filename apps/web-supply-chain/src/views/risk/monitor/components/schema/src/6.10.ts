import type { SchemaItem } from '../index';

export const name: string = '启信分变更';
export const schema: SchemaItem[] = [
  { fieldName: 'score', label: '启信分', isChange: true },
  { fieldName: 'capital_background_score', label: '资金背景' },
  { fieldName: 'scale_score', label: '企业规模' },
  { fieldName: 'growth_score', label: '成长性' },
  { fieldName: 'intellectual_property_score', label: '知识产权' },
  { fieldName: 'quality_score', label: '经营质量' },
  { fieldName: 'capital_background_rank', label: '资本背景-行业排名' },
  { fieldName: 'scale_rank', label: '企业规模-行业排名' },
  { fieldName: 'growth_rank', label: '成长性-行业排名' },
  { fieldName: 'intellectual_property_rank', label: '知识产权-行业排名' },
  { fieldName: 'quality_rank', label: '经营质量-行业排名' },
  { fieldName: 'risk_rank', label: '风险状况-行业排名' },
  { fieldName: 'positive_score', label: '实力分' },
  { fieldName: 'negative_score', label: '风险分' },
  { fieldName: 'final_score', label: '加权得分' },
  { fieldName: 'total_rank', label: '启信分-行业排名' },
  { fieldName: 'score_level', label: '评级' },
];
