import type { SchemaItem } from '../index';

export const name: string = '未准入境';
export const schema: SchemaItem[] = [
  { fieldName: 'product_type', label: '产品类型' },
  { fieldName: 'HS', label: 'HS编码' },
  { fieldName: 'test_number', label: '检验检疫编号' },
  { fieldName: 'product_name', label: '产品名称' },
  { fieldName: 'brand', label: '品牌' },
  { fieldName: 'nation', label: '产地' },
  { fieldName: 'origin_nation', label: '原产地' },
  { fieldName: 'ename', label: '生产企业名称' },
  { fieldName: 'importer_name', label: '进口商名称' },
  { fieldName: 'record_no', label: '进口商备案号' },
  { fieldName: 'quality', label: '数量/重量' },
  { fieldName: 'reason', label: '未准入境的事实/不合格原因' },
  { fieldName: 'measure', label: '处置措施' },
  { fieldName: 'city', label: '进境口岸' },
  { fieldName: 'release_date', label: '发布日期' },
];
