import type { SchemaItem } from '../index';

import { sexDictList, typeDictList } from '../../constant';
import { formatDictValue } from '../../utils';

export const name: string = '失信人';
export const schema: SchemaItem[] = [
  { fieldName: 'execution_status', label: '被执行人的履行情况', isChange: true },
  { fieldName: 'amount', label: '标的（元）' },
  { fieldName: 'case_number', label: '案号' },
  { fieldName: 'date', label: '立案时间' },
  { fieldName: 'doc_number', label: '执行依据文号' },
  { fieldName: 'publish_date', label: '公布日期' },
  { fieldName: 'ex_department', label: '做出执行依据单位' },
  { fieldName: 'oper_name', label: '法人名称' },
  { fieldName: 'province', label: '省份' },
  { fieldName: 'execution_desc', label: '失信被执行人具体行为情形' },
  { fieldName: 'final_duty', label: '生效法律文书确定的义务' },
  { fieldName: 'sex', label: '性别', formatter: formatDictValue(sexDictList) },
  { fieldName: 'age', label: '年龄' },
  { fieldName: 'number', label: '企业证照号' },
  { fieldName: 'type', label: '当事人类型', formatter: formatDictValue(typeDictList) },
  { fieldName: 'concern_count', label: '关注数' },
  { fieldName: 'status', label: '状态' },
  { fieldName: 'case_relation', label: '是否有案件关联' },
  { fieldName: 'court', label: '法院' },
  { fieldName: 'ename', label: '企业名称' },
  { fieldName: 'p_ename', label: '个人所在公司名称' },
  {
    fieldName: 'related_companies',
    label: '关联企业',
    schema: [
      { fieldName: 'name', label: '相关方名称' },
      { fieldName: 'type', label: '相关方类型', formatter: formatDictValue(typeDictList) },
    ],
  },
];

export const handleKeys = {
  related_companies: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
