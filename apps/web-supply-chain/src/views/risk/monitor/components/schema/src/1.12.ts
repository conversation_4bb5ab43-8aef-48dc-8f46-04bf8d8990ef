import type { SchemaItem } from '../index';

export const name: string = '工商股东';
export const schema: SchemaItem[] = [
  { fieldName: 'stock_name', label: '股东名称' },
  { fieldName: 'stock_percent', label: '持股比例（%）', isChange: true },
  { fieldName: 'should_capi_conv', label: '认缴额（万元）' },
  { fieldName: 'currency', label: '币种' },
  { fieldName: 'should_capi', label: '认缴额（万）' },
  { fieldName: 'stock_num', label: '持股数（股）' },
  { fieldName: 'ename', label: '企业名称' },
];
