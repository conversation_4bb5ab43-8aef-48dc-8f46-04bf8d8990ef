import type { SchemaItem } from '../index';

export const name: string = '土地抵押';
export const schema: SchemaItem[] = [
  { fieldName: 'name_before', label: '之前公司名' },
  { fieldName: 'name_after', label: '之后公司名' },
  { fieldName: 'release_time', label: '发布时间' },
  { fieldName: 'area', label: '总面积（平方米）' },
  { fieldName: 'land_use', label: '土地用途' },
  { fieldName: 'request_type', label: '类型' },
  { fieldName: 'code', label: '宗地编号' },
  { fieldName: 'use_code', label: '土地使用权证号' },
  { fieldName: 'diya_area', label: '抵押面积（平方米）' },
  { fieldName: 'rent_amount', label: '抵押金额（万元）' },
  { fieldName: 'shuxing', label: '属性' },
  { fieldName: 'entity_type_before', label: '企业类型标识' },
  { fieldName: 'entity_type_after', label: '企业类型标识（之后）' },
  { fieldName: 'gsks', label: '公示开始' },
  { fieldName: 'gsjs', label: '公示结束' },
  { fieldName: 'rent_year', label: '出租期限' },
];
