import type { SchemaItem } from '../index';

export const name: string = '简易注销';
export const schema: SchemaItem[] = [
  { fieldName: 'notice_period', label: '公告期' },
  { fieldName: 'ename', label: '企业名称' },
  {
    fieldName: 'gs_sca_result',
    label: '简易注销结果',
    schema: [
      { fieldName: 'date', label: '注销时间' },
      { fieldName: 'result', label: '注销结果' },
    ],
  },
  {
    fieldName: 'gs_sca_objections',
    label: '异议信息',
    schema: [
      { fieldName: 'proposer', label: '处理机关' },
      { fieldName: 'seq_no', label: '序列号' },
      { fieldName: 'content', label: '异议内容' },
      { fieldName: 'date', label: '异议时间' },
    ],
  },
];
export const handleKeys = {
  gs_sca_result: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
  gs_sca_objections: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
