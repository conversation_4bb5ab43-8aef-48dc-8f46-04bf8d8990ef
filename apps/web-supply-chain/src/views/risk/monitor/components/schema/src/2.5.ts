import type { SchemaItem } from '../index';

export const name: string = '经营异常';
export const schema: SchemaItem[] = [
  { fieldName: 'in_date', label: '列入日期', isChange: true },
  { fieldName: 'out_date', label: '移出日期', isChange: true },
  { fieldName: 'out_department', label: '作出决定机关（移出）', isChange: true },
  { fieldName: 'department', label: '做出决定机关(列入)' },
  { fieldName: 'in_reason', label: '列入经营异常名录原因' },
  { fieldName: 'out_reason', label: '移出经营异常名录原因' },
];
