import type { SchemaItem } from '../index';

export const name: string = '双随机抽查';
export const schema: SchemaItem[] = [
  { fieldName: 'insAuth', label: '抽查机关' },
  { fieldName: 'insDate', label: '抽查完成日期' },
  { fieldName: 'raninsPlanId', label: '抽查计划编号' },
  { fieldName: 'raninsPlaneName', label: '抽查计划名称' },
  { fieldName: 'raninsTaskId', label: '抽查任务编号' },
  { fieldName: 'raninsTaskName', label: '抽查任务名称' },
  { fieldName: 'raninsTypeName', label: '抽查类型' },
];
