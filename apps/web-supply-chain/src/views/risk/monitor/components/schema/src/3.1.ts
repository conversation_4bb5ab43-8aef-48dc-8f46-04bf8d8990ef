import type { SchemaItem } from '../index';

import { typeDictList } from '../../constant';
import { formatDictValue } from '../../utils';

export const name: string = '裁判文书';
export const schema: SchemaItem[] = [
  { fieldName: 'case_cause', label: '案由' },
  { fieldName: 'case_no', label: '案号' },
  { fieldName: 'case_relation', label: '是否有案件关联' },
  { fieldName: 'court', label: '法院' },
  { fieldName: 'date', label: '判决时间' },
  { fieldName: 'doc_type', label: '文书类型' },
  { fieldName: 'judgeresult', label: '审判结果' },
  { fieldName: 'pub_date', label: '发布日期' },
  { fieldName: 'role', label: '角色' },
  { fieldName: 'title', label: '标题' },
  { fieldName: 'ename', label: '企业名称' },
  { fieldName: 'type', label: '裁判文书类型' },
  {
    fieldName: 'related_companies',
    label: '关联企业/个人',
    schema: [
      { fieldName: 'clean_role', label: '角色' },
      { fieldName: 'name', label: '名称' },
      { fieldName: 'type', label: '类型', formatter: formatDictValue(typeDictList) },
    ],
  },
];

export const handleKeys = {
  related_companies: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
