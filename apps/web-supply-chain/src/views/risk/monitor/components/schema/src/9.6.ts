import type { SchemaItem } from '../index';

export const name: string = '债券违约';
export const schema: SchemaItem[] = [
  { fieldName: 'secinnercode', label: '证券内码' },
  { fieldName: 'securitycode', label: '债券代码' },
  { fieldName: 'companyname', label: '公司名称' },
  { fieldName: 'noticedate', label: '公告日期' },
  { fieldName: 'noticetitle', label: '公告标题' },
  { fieldName: 'eventtype', label: '事件类型' },
  { fieldName: 'attachname', label: '附件url' },
  { fieldName: 'wtycompname', label: '担保人' },
];
