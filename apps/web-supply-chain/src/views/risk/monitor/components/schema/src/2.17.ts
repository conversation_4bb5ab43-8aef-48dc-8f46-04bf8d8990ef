import type { SchemaItem } from '../index';

export const name: string = '询价评估';
export const schema: SchemaItem[] = [
  { fieldName: 'case_no', label: '案号' },
  { fieldName: 'court_name', label: '法院名称' },
  { fieldName: 'property_kind', label: '财产类型' },
  { fieldName: 'subject_matter', label: '标的物名称' },
  { fieldName: 'determine_style', label: '确定参考价方式' },
  { fieldName: 'pdf', label: '询价结果公示', span: 2 },
  { fieldName: 'content', label: '询价结果正文', span: 2 },
  { fieldName: 'case_cause', label: '案由' },
  { fieldName: 'pub_date', label: '发布日期' },
  { fieldName: 'lottery_time', label: '摇号时间' },
  { fieldName: 'evaluation_institution', label: '选定评估机构公示' },
  { fieldName: 'type', label: '类型' },
  {
    fieldName: 'subject_matter_people',
    label: '标的物所有人',
    schema: [
      { fieldName: 'name', label: '标的物所有人名称' },
      { fieldName: 'type', label: '标的物所有人类型' },
    ],
  },
  {
    fieldName: 'result',
    label: '询价结果',
    schema: [
      { fieldName: 'price', label: '价格' },
      { fieldName: 'name', label: '询价名称' },
      { fieldName: 'type', label: '询价类型' },
    ],
  },
];

export const handleKeys = {
  subject_matter_people: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
  result: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
  evaluation_institution: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
