import type { SchemaItem } from '../index';

export const name: string = '行政许可';
export const schema: SchemaItem[] = [
  { fieldName: 'name', label: '许可文件名称', isChange: true },
  { fieldName: 'content', label: '许可内容' },
  { fieldName: 'department', label: '许可机关' },
  { fieldName: 'end_date', label: '有效期至' },
  { fieldName: 'number', label: '许可文件编号' },
  { fieldName: 'start_date', label: '有效期自' },
  { fieldName: 'status', label: '状态' },
];
