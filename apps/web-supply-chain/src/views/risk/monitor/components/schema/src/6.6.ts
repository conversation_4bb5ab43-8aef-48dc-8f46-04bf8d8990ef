import type { SchemaItem } from '../index';

export const name = '集成电路布图设计专有权';
export const schema: SchemaItem[] = [
  { fieldName: 'role', label: '角色', isChange: true },
  { fieldName: 'registration_no', label: '布图设计登记号' },
  { fieldName: 'application_date', label: '布图设计申请日' },
  { fieldName: 'application_year', label: '布图设计申请年份' },
  { fieldName: 'notice_date', label: '公告日期' },
  { fieldName: 'design_name', label: '布图设计名称' },
  { fieldName: 'ename', label: '企业名称' },
  {
    fieldName: 'designer',
    label: '布图设计创作人',
    schema: [{ fieldName: 'ename', label: '企业名称' }],
  },
];

export const handleKeys = {
  designer: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
