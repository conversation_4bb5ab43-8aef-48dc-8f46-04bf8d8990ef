import type { SchemaItem } from '../index';

export const name: string = '股权冻结';
export const schema: SchemaItem[] = [
  { fieldName: 'amount', label: '股权金额' },
  { fieldName: 'be_executed_person', label: '被执行人' },
  { fieldName: 'number', label: '协助公示通知书文号' },
  { fieldName: 'status', label: '状态' },
  { fieldName: 'detail_freeze_start_date', label: '冻结起始日期' },
  { fieldName: 'ename', label: '企业名称' },
  { fieldName: 'detail_public_date', label: '发布日期' },
  {
    fieldName: 'continue_freeze_details',
    label: '续行冻结详情',
    schema: [
      { fieldName: 'assist_name', label: '被执行人' },
      { fieldName: 'execute_court', label: '执行法院' },
      { fieldName: 'freeze_amount', label: '被执行人持有股权、其它投资权益的数额' },
      { fieldName: 'corp_name', label: '关联名称' },
      { fieldName: 'adjudicate_no', label: '执行裁定文书号' },
      { fieldName: 'continue_date_start', label: '续行冻结开始日期' },
      { fieldName: 'continue_date_end', label: '续行冻结结束日期' },
      { fieldName: 'public_date', label: '公示日期' },
      { fieldName: 'assist_ident_no', label: '被执行人证件号码' },
      { fieldName: 'assist_item', label: '执行事项' },
      { fieldName: 'notice_no', label: '执行通知文书号' },
      { fieldName: 'assist_ident_type', label: '被执行人证件种类' },
    ],
  },
  {
    fieldName: 'detail',
    label: '冻结详情',
    schema: [
      { fieldName: 'assist_name', label: '被执行人名称' },
      { fieldName: 'execute_court', label: '执行法院' },
      { fieldName: 'adjudicate_no', label: '执行裁定文书号' },
      { fieldName: 'freeze_amount', label: '被执行人持有股权、其它投资权益的数额' },
      { fieldName: 'assist_ident_no', label: '被执行人证件号码' },
      { fieldName: 'freeze_start_date', label: '冻结开始日期' },
      { fieldName: 'freeze_end_date', label: '冻结结束日期' },
      { fieldName: 'public_date', label: '公示日期' },
      { fieldName: 'assist_ident_type', label: '被执行人证件种类' },
      { fieldName: 'corp_name', label: '关联名称' },
      { fieldName: 'assist_item', label: '执行事项' },
      { fieldName: 'notice_no', label: '执行通知文书号' },
      { fieldName: 'freeze_year_month', label: '冻结详情中的冻结期限' },
    ],
  },
  {
    fieldName: 'lose_efficacy',
    label: '失效信息',
    schema: [
      { fieldName: 'date', label: '失效时间' },
      { fieldName: 'reason', label: '失效原因' },
    ],
  },
  {
    fieldName: 'un_freeze_details',
    label: '股权冻结-解冻详情',
    schema: [
      { fieldName: 'assist_name', label: '被执行人' },
      { fieldName: 'execute_court', label: '执行法院' },
      { fieldName: 'freeze_amount', label: '被执行人持有股权、其它投资权益的数额' },
      { fieldName: 'corp_name', label: '关联名称' },
      { fieldName: 'adjudicate_no', label: '执行裁定文书号' },
      { fieldName: 'unfreeze_date', label: '解冻日期' },
      { fieldName: 'public_date', label: '公示日期' },
      { fieldName: 'assist_ident_no', label: '被执行人证件号码' },
      { fieldName: 'eid', label: '被执行人企业eid' },
      { fieldName: 'assist_item', label: '执行事项' },
      { fieldName: 'notice_no', label: '执行通知文书号' },
      { fieldName: 'assist_ident_type', label: '被执行人证件种类' },
    ],
  },
];

export const handleKeys = {
  continue_freeze_details: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
  detail: (value: string) => {
    return value ? JSON.parse(value) : {};
  },
  lose_efficacy: (value: string) => {
    return value ? JSON.parse(value) : {};
  },
  un_freeze_details: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
