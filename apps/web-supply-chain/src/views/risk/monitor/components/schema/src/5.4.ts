import type { SchemaItem } from '../index';

import { formatDictValue } from '../../utils';

const preciseDictList = [
  { label: '准确值', value: '0' },
  { label: '不准确', value: '1' },
];

export const name = '融资动态';
export const schema: SchemaItem[] = [
  { fieldName: 'round', label: '轮次', isChange: true },
  { fieldName: 'amount', label: '投资金额' },
  { fieldName: 'currency', label: '币种' },
  { fieldName: 'date', label: '融资日期' },
  { fieldName: 'investors', label: '本轮投资人' },
  { fieldName: 'precise', label: '准确性', formatter: formatDictValue(preciseDictList) },
  { fieldName: 'publish_date', label: '发布日期' },
  { fieldName: 'ename', label: '企业名称' },
];
