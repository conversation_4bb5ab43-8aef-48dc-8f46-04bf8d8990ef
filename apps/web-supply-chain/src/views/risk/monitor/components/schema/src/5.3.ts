import type { SchemaItem } from '../index';

export const name: string = '招聘信息';
export const schema: SchemaItem[] = [
  { fieldName: 'title', label: '招聘描述', isChange: true },
  { fieldName: 'age', label: '年龄' },
  { fieldName: 'date', label: '发布日期' },
  { fieldName: 'education', label: '学历' },
  { fieldName: 'description', label: '职位描述', span: 2 },
  { fieldName: 'end_date', label: '结束招聘日期' },
  { fieldName: 'job_type', label: '工作类型' },
  { fieldName: 'number', label: '招聘人数' },
  { fieldName: 'location', label: '工作地点' },
  { fieldName: 'salary', label: '薪资' },
  { fieldName: 'sex', label: '性别' },
  { fieldName: 'start_date', label: '开始招聘日期' },
  { fieldName: 'years', label: '工作经验' },
];
