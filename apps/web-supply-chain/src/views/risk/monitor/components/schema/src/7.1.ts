import type { SchemaItem } from '../index';

export const name: string = '欠税信息';
export const schema: SchemaItem[] = [
  { fieldName: 'overdue_amount', label: '欠税余额/合计', isChange: true },
  { fieldName: 'curr_overdue_amount', label: '当前新发生的欠税余额/本年度新欠余额' },
  { fieldName: 'history_overdue_amount', label: '以前年度陈欠余额' },
  { fieldName: 'overdue_time', label: '发生欠税的时间' },
  { fieldName: 'overdue_type', label: '欠税税种' },
  { fieldName: 'pub_date', label: '公布时间' },
  { fieldName: 'taxpayer_num', label: '纳税人识别号/税务登记号' },
];
