import type { SchemaItem } from '../index';

export const name: string = '事业单位证书时间变更';
export const schema: SchemaItem[] = [
  { fieldName: 'certificate_start_date', label: '证书生效日期', isChange: true },
  { fieldName: 'certificate_end_date', label: '证书失效日期', isChange: true },
  { fieldName: 'currency_unit', label: '货币单位' },
  { fieldName: 'regist_capi_new', label: '注册资本数字' },
  { fieldName: 'agency', label: '登记管理机关' },
  { fieldName: 'oper_name', label: '法定代表人' },
  { fieldName: 'org_type', label: '组织类型' },
  { fieldName: 'status', label: '登记状态' },
];
