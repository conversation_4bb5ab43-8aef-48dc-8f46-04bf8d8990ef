import type { SchemaItem } from '../index';

import { typeDictList } from '../../constant';
import { formatDictValue } from '../../utils';

export const name = '劳动仲裁送达公告';
export const schema: SchemaItem[] = [
  { fieldName: 'role', label: '角色' },
  { fieldName: 'case_no', label: '案号' },
  { fieldName: 'doc_id', label: '仲裁编号' },
  { fieldName: 'date', label: '公告时间' },
  { fieldName: 'department', label: '公告人' },
  { fieldName: 'title', label: '标题' },
  { fieldName: 'case_reason', label: '案由', span: 2 },
  { fieldName: 'content', label: '公告详情', span: 2 },
  {
    fieldName: 'related_companies',
    label: '关联企业/人员',
    schema: [
      { fieldName: 'role', label: '角色' },
      { fieldName: 'name', label: '名称' },
      { fieldName: 'type', label: '类型', formatter: formatDictValue(typeDictList) },
    ],
  },
];

export const handleKeys = {
  related_companies: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
