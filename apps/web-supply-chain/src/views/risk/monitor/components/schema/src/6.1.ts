import type { SchemaItem } from '../index';

export const name: string = '专利信息';
export const schema: SchemaItem[] = [
  { fieldName: 'patent_name', label: '专利名称', isChange: true },
  { fieldName: 'category_num', label: '分类号' },
  { fieldName: 'outhor_date', label: '公告日期' },
  { fieldName: 'outhor_num', label: '公告号' },
  { fieldName: 'patent_person', label: '专利权人' },
  { fieldName: 'request_date', label: '申请日期' },
  { fieldName: 'request_num', label: '申请号' },
  { fieldName: 'type_name', label: '专利类型' },
  { fieldName: 'brief', label: '专利摘要', span: 2 },
];
