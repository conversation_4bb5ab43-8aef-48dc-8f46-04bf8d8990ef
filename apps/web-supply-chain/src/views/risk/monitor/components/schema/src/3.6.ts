import type { SchemaItem } from '../index';

export const name: string = '票据承兑';
export const schema: SchemaItem[] = [
  { fieldName: 'overdue_remain', label: '逾期余额', isChange: true },
  { fieldName: 'closing_date', label: '披露信息时点日期' },
  { fieldName: 'ename', label: '企业名称' },
  { fieldName: 'account_name', label: '承兑人开户机构' },
  { fieldName: 'amount_add', label: '累计承兑发生额（元）' },
  { fieldName: 'amount_remain', label: '承兑余额（元）' },
  { fieldName: 'overdue_add', label: '累计逾期发生额（元）' },
  { fieldName: 'medium', label: '票据介质' },
  { fieldName: 'release_date', label: '披露日期' },
];
