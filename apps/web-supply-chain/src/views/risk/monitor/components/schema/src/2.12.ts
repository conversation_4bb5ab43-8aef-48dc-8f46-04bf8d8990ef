import type { SchemaItem } from '../index';

export const name: string = '环保处罚';
export const schema: SchemaItem[] = [
  { fieldName: 'punishment_date', label: '处罚日期' },
  { fieldName: 'province', label: '省份' },
  { fieldName: 'punish_amnt', label: '罚款金额（万元）' },
  {
    fieldName: 'punishments',
    label: '处罚内容',
    schema: [
      { fieldName: 'illegal_type', label: '违法类型/类别' },
      { fieldName: 'punishment_title', label: '处罚名称' },
      { fieldName: 'punishment_date', label: '处罚日期/处罚生效期' },
      { fieldName: 'end_date', label: '废止日期/处罚截止期' },
      { fieldName: 'documentNo', label: '文书号' },
      { fieldName: 'implement_status', label: '执行情况' },
      { fieldName: 'punishment_reason', label: '处罚事由/违法事实' },
      { fieldName: 'punishment_basis', label: '处罚依据' },
      { fieldName: 'punishment_measure', label: '处罚措施' },
      { fieldName: 'punishment_content', label: '处罚内容' },
      { fieldName: 'punishment_dept', label: '作出处罚单位名' },
      { fieldName: 'punishment_result', label: '处罚结果' },
      { fieldName: 'punishment_type', label: '处罚类别' },
      { fieldName: 'update_date', label: '数据更新时间' },
      { fieldName: 'now_status', label: '当前状态' },
      { fieldName: 'remarks', label: '备注' },
      { fieldName: 'document_url', label: '文书url', span: 2 },
    ],
  },
];
export const handleKeys = {
  punishments: (value: string) => {
    return value ? JSON.parse(value) : [];
  },
};
