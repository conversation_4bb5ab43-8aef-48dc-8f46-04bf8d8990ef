import type { SchemaItem } from '../index';

export const name: string = '海关登记信息';
export const schema: SchemaItem[] = [
  { fieldName: 'customs_num', label: '海关注册编码', isChange: true },
  { fieldName: 'administrative_divisions', label: '行政区划' },
  { fieldName: 'annual_report', label: '年报情况' },
  { fieldName: 'business_category', label: '经营类别' },
  { fieldName: 'cancel_flag', label: '海关注销标志' },
  { fieldName: 'customs_reg', label: '注册海关' },
  { fieldName: 'economic_regions', label: '经济区划' },
  { fieldName: 'elect_type', label: '跨境贸易电子商务类型' },
  { fieldName: 'industry_type', label: '行业种类' },
  { fieldName: 'special_area', label: '特殊贸易区域' },
  { fieldName: 'reg_date', label: '注册日期' },
  { fieldName: 'vail_date', label: '报关有效期' },
];
