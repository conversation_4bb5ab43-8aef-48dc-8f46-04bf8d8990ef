import type { SchemaItem } from '../index';

export const name: string = '重大税收违法';
export const schema: SchemaItem[] = [
  { fieldName: 'case_type', label: '案件性质', isChange: true },
  { fieldName: 'time', label: '发生时间' },
  { fieldName: 'type', label: '公示税务机关' },
  { fieldName: 'tax_num', label: '纳税人识别号' },
  { fieldName: 'start_date', label: '开始时间' },
  { fieldName: 'end_date', label: '结束时间' },
  { fieldName: 'pub_department', label: '所属税务机关' },
  { fieldName: 'check_department', label: '检查机关' },
  { fieldName: 'truth', label: '违法事实' },
  { fieldName: 'law_punishment', label: '法律依据及处罚' },
  { fieldName: 'police', label: '已送公安情况' },
  { fieldName: 'org_code', label: '组织机构代码' },
  { fieldName: 'area', label: '经营地点' },
];
