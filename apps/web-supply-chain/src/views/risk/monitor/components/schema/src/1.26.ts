import type { SchemaItem } from '../index';

export const name: string = '大股东变更';
export const schema: SchemaItem[] = [
  { fieldName: 'stock_name', label: '股东名' },
  { fieldName: 'stock_type', label: '股东类型' },
  { fieldName: 'share_type', label: '股份类型' },
  { fieldName: 'stock_percent', label: '出资比例' },
  { fieldName: 'should_capi_conv', label: '认缴额（万）' },
  { fieldName: 'real_capi', label: '实缴额（万）' },
  { fieldName: 'stock_num', label: '持股数' },
  { fieldName: 'con_date', label: '出资日期' },
  { fieldName: 'country', label: '股东国别' },
  { fieldName: 'real_capi_date', label: '实缴出资日期' },
];
