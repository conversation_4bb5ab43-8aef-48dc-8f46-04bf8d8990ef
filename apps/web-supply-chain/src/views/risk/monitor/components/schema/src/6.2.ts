import type { SchemaItem } from '../index';

export const name: string = '商标信息';
export const schema: SchemaItem[] = [
  { fieldName: 'trademark_name', label: '商标名称', isChange: true },
  { fieldName: 'apply_date', label: '申请日期' },
  { fieldName: 'name', label: '商标名称' },
  { fieldName: 'reg_number', label: '注册号' },
  { fieldName: 'status', label: '商标状态' },
  { fieldName: 'type_num', label: '国际分类号' },
];
