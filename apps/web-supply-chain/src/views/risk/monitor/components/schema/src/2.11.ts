import type { SchemaItem } from '../index';

import { formatDictValue } from '../../utils';

const caseKindDictList = [
  { label: '破产审查案件', value: '1' },
  { label: '破产案件', value: '2' },
  { label: '强制清算申请审查案件', value: '3' },
  { label: '强制清算案件', value: '4' },
  { label: '强制清算上诉案件', value: '5' },
  { label: '破产上诉案件', value: '6' },
  { label: '破产监督案件为破产案件', value: '7' },
  { label: '强制清算监督案件为强制清算', value: '8' },
];

export const name: string = '破产案件';
export const schema: SchemaItem[] = [
  { fieldName: 'app_name', label: '申请人姓名' },
  { fieldName: 'case_kind', label: '案件类型', formatter: formatDictValue(caseKindDictList) },
  { fieldName: 'name', label: '被申请人' },
  { fieldName: 'management_agency', label: '管理人机构' },
  { fieldName: 'court', label: '办理法院' },
  { fieldName: 'case_no', label: '案件号' },
  { fieldName: 'agency_principal', label: '管理人主要负责人' },
  { fieldName: 'pub_date', label: '公开时间' },
];
