import type { SchemaItem } from '../index';

export const name: string = '高新企业';
export const schema: SchemaItem[] = [
  { fieldName: 'register_no', label: '许可证号' },
  { fieldName: 'year', label: '年份' },
  { fieldName: 'level', label: '级别' },
  { fieldName: 'issue_unit', label: '许可机构' },
  { fieldName: 'end_date', label: '资质终止日期/撤销日期' },
  { fieldName: 'valid_start', label: '入库登记日期/有效期起' },
  { fieldName: 'valid_end', label: '有效期至' },
  { fieldName: 'state', label: '许可证状态' },
];
