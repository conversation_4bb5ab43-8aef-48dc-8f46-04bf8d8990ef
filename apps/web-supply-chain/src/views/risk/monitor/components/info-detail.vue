<script setup lang="ts">
import type { MonitorCompanyInfo } from '#/api';

import { ref, watch } from 'vue';

import { DynamicDescriptions } from '@vben/base-ui';
import { DESCRIPTIONS_PROP } from '@vben/constants';

import { Descriptions, DescriptionsItem, TypographyTitle } from 'ant-design-vue';

import { schemaList } from '#/views/risk/monitor/components/schema';
import { deepClean } from '#/views/risk/monitor/components/utils';

const props = defineProps({
  detail: {
    type: Object,
    required: true,
  },
});
const originalData = ref<MonitorCompanyInfo>({});
const infoData = ref({});
const schema = ref<{ fieldName: string; label: string }[]>([]);
const init = (detail: MonitorCompanyInfo) => {
  // console.log('原始信息', detail);
  let changeData: { [key: string]: any } = {};
  originalData.value = detail;
  const typeConfig = schemaList.find((item) => item.name === detail.changeTableName);
  schema.value = typeConfig?.schema ?? [];
  if (detail.changeAfter && Object.keys(JSON.parse(detail.changeAfter)).length > 0) {
    changeData = JSON.parse(detail.changeAfter);
  } else if (detail.changeBefore && Object.keys(JSON.parse(detail.changeBefore)).length > 0) {
    changeData = JSON.parse(detail.changeBefore);
  }
  // console.log('处理前数据', structuredClone(changeData));
  const handleKeys = typeConfig?.handleKeys ?? {};
  Object.keys(changeData).forEach((key: string) => {
    if (handleKeys[key]) {
      changeData[key] = handleKeys[key](changeData[key]);
    }
  });
  const valueKey = typeConfig?.valueKey;
  if (valueKey) {
    changeData = changeData[valueKey];
  }
  infoData.value = deepClean(changeData);
  // console.log('最终处理后的数据', infoData.value);
};
watch(() => props.detail, init, {
  immediate: true,
});
</script>

<template>
  <div>
    <Descriptions v-bind="DESCRIPTIONS_PROP" class="mb-4">
      <DescriptionsItem label="公司名称">{{ originalData.name }}</DescriptionsItem>
      <DescriptionsItem label="社会信用代码">{{ originalData.creditNo }}</DescriptionsItem>
      <DescriptionsItem label="变更维度">{{ originalData.changeTableName }}</DescriptionsItem>
      <DescriptionsItem label="变更类型">{{ originalData.changeType }}</DescriptionsItem>
    </Descriptions>
    <TypographyTitle :level="5">变更信息</TypographyTitle>
    <DynamicDescriptions :data="infoData" :schema="schema" v-bind="DESCRIPTIONS_PROP" />
  </div>
</template>

<style></style>
