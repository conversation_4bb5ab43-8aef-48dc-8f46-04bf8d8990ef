<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MonitorCompanyInfo } from '#/api';

import { ref } from 'vue';

import { Page, useVbenModal } from '@vben/common-ui';
import { defineFormOptions } from '@vben/utils';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import { getMonitorInfoPageListApi } from '#/api/risk/monitor/company';

import InfoDetail from '../components/info-detail.vue';

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'name',
      label: '企业名称',
    },
    {
      component: 'Input',
      fieldName: 'changeTableName',
      label: '变更维度',
    },
  ],
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'name', title: '企业名称' },
    { field: 'creditNo', title: '社会信用代码' },
    { field: 'changeTableName', title: '变更维度' },
    {
      field: 'changeType',
      title: '变更类型',
      cellRender: { name: 'CellStatus', props: { code: 'MONITOR_CHANGE_TYPE' } },
    },
    {
      field: 'riskClass',
      title: '风险等级',
      cellRender: {
        name: 'CellStatus',
        props: {
          code: 'MONITOR_RISK_CLASS',
          option: { dictValueType: 'number' },
        },
      },
    },
    { field: 'changeDate', title: '变更时间', formatter: 'formatDate' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getMonitorInfoPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};
const [Grid] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const [Modal, modalApi] = useVbenModal({
  cancelText: '关闭',
  showConfirmButton: false,
});
const infoDetail = ref({});
const view = (row: MonitorCompanyInfo) => {
  infoDetail.value = row;
  modalApi.open();
};
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="view(row)">详情</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <Modal title="监控详情" class="w-[1000px]">
      <InfoDetail :detail="infoDetail" />
    </Modal>
  </Page>
</template>

<style></style>
