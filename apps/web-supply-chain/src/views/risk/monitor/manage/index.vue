<script setup lang="ts">
import type { VbenFormProps } from '@vben/common-ui';

import type { VxeTableGridOptions } from '#/adapter/vxe-table';
import type { MonitorCompanyInfo } from '#/api';

import { computed, reactive, ref } from 'vue';

import { confirm, Page, useVbenModal } from '@vben/common-ui';
import { FORM_PROP } from '@vben/constants';
import { defineFormOptions } from '@vben/utils';

import { cloneDeep, debounce } from 'lodash-es';

import { useVbenVxeGrid } from '#/adapter/vxe-table';
import {
  addMonitorCompanyApi,
  deleteMonitorCompanyApi,
  editMonitorCompanyApi,
  getMonitorCompanyPageListApi,
  searchCompanyListApi,
} from '#/api';

const state = reactive({
  fetching: false,
  formProp: {
    ...FORM_PROP,
    labelCol: { span: 6 },
    wrapperCol: { span: 18 },
  },
});

const formOptions: VbenFormProps = defineFormOptions({
  schema: [
    {
      component: 'Input',
      fieldName: 'companyName',
      label: '企业名称',
    },
    {
      component: 'Select',
      fieldName: 'status',
      label: '状态',
      componentProps: {
        options: [],
        allowClear: true,
      },
    },
    {
      component: 'RangePicker',
      fieldName: 'scoringDate',
      label: '添加日期',
    },
  ],
  fieldMappingTime: [['scoringDate', ['scoringStartDate', 'scoringEndDate'], 'x']],
});
const gridOptions: VxeTableGridOptions = {
  columns: [
    { type: 'seq', title: '序号', width: 60 },
    { field: 'companyName', title: '企业名称' },
    { field: 'companyCode', title: '社会信用代码' },
    { field: 'startTime', title: '添加日期', formatter: 'formatDate' },
    { field: 'isMonitor', title: '企业监控', formatter: ['formatBoolean', { true: '启用', false: '禁用' }] },
    { field: 'isNewsMonitor', title: '舆情监控', formatter: ['formatBoolean', { true: '启用', false: '禁用' }] },
    { field: 'status', title: '状态' },
    {
      field: 'action',
      title: '操作',
      fixed: 'right',
      width: 160,
      slots: { default: 'action' },
    },
  ],
  height: 'auto',
  proxyConfig: {
    ajax: {
      query: async ({ page }, formValues) => {
        return await getMonitorCompanyPageListApi({
          current: page.currentPage,
          size: page.pageSize,
          ...formValues,
        });
      },
    },
  },
};
const [Grid, gridApi] = useVbenVxeGrid({
  formOptions,
  gridOptions,
});
const searchCompanyList = ref([]);
const monitorCompanyFormRef = ref();
const monitorCompanyForm = ref<MonitorCompanyInfo>({
  companyName: '',
  companyCode: '',
});
const isMonitor = computed({
  get() {
    return !!monitorCompanyForm.value.isMonitor;
  },
  set(newValue: boolean) {
    monitorCompanyForm.value.isMonitor = newValue ? 1 : 0;
  },
});
const isNewsMonitor = computed({
  get() {
    return !!monitorCompanyForm.value.isNewsMonitor;
  },
  set(newValue: boolean) {
    monitorCompanyForm.value.isNewsMonitor = newValue ? 1 : 0;
  },
});
const rules = {
  companyCode: [{ required: true, message: '请选择企业' }],
};
const [Modal, modalApi] = useVbenModal({
  onConfirm: async () => {
    await monitorCompanyFormRef.value.validate();
    const api = monitorCompanyForm.value.id ? editMonitorCompanyApi : addMonitorCompanyApi;
    modalApi.lock();
    try {
      await api(monitorCompanyForm.value);
      await modalApi.close();
      await gridApi.reload();
    } finally {
      modalApi.unlock();
    }
  },
  onClosed() {
    monitorCompanyFormRef.value.resetFields();
    monitorCompanyForm.value = {
      companyName: '',
      companyCode: '',
    };
  },
});
const add = async () => {
  modalApi.setState({ title: '新增监控企业' });
  modalApi.open();
};
const edit = (row: MonitorCompanyInfo) => {
  modalApi.setState({ title: '编辑监控企业' });
  monitorCompanyForm.value = cloneDeep(row);
  modalApi.open();
};
const del = async (row: MonitorCompanyInfo) => {
  await confirm('确认删除此数据？', '确认删除');
  await deleteMonitorCompanyApi(row);
  await gridApi.reload();
};
const changeCompany = (val: string, option: any) => {
  if (val) {
    monitorCompanyForm.value.companyCode = option.credit_no;
  }
};
const handleSearch = debounce(async (val: string) => {
  if (!val) {
    searchCompanyList.value = [];
    return false;
  }
  let resList = [];
  try {
    state.fetching = true;
    const { data: res = [] } = (await searchCompanyListApi({ keyword: val })) ?? [];
    resList = res.map((item: any) => ({
      label: item.name,
      value: item.name,
      ...item,
    }));
  } finally {
    state.fetching = false;
  }
  searchCompanyList.value = resList;
}, 800);
</script>

<template>
  <Page auto-content-height>
    <Grid>
      <template #toolbar-actions>
        <a-button type="primary" @click="add">新增</a-button>
      </template>
      <template #action="{ row }">
        <a-space>
          <a-typography-link @click="edit(row)">编辑</a-typography-link>
          <a-typography-link type="danger" @click="del(row)">删除</a-typography-link>
        </a-space>
      </template>
    </Grid>
    <Modal>
      <a-form ref="monitorCompanyFormRef" :model="monitorCompanyForm" :rules="rules" v-bind="state.formProp">
        <a-form-item label="企业名称" name="companyCode">
          <a-select
            v-model:value="monitorCompanyForm.companyName"
            show-search
            :options="searchCompanyList"
            :dropdown-match-select-width="false"
            :filter-option="false"
            :list-height="512"
            placement="bottomLeft"
            @search="handleSearch"
            @change="changeCompany"
          >
            <template v-if="state.fetching" #notFoundContent>
              <a-spin size="small" />
            </template>
            <template #option="option">
              {{ option.name }} | 社会信用代码：{{ option.credit_no }} | 注册号：{{ option.reg_no }} | 法人：{{
                option.oper_name
              }}
            </template>
          </a-select>
        </a-form-item>
        <a-form-item label="监控项目">
          <a-checkbox v-model:checked="isMonitor">企业监控</a-checkbox>
          <a-checkbox v-model:checked="isNewsMonitor">舆情监控</a-checkbox>
        </a-form-item>
      </a-form>
    </Modal>
  </Page>
</template>

<style></style>
