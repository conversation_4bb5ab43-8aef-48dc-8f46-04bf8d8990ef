<script setup lang="ts">
import type { UploadChangeParam } from 'ant-design-vue';
import type { UploadRequestOption } from 'ant-design-vue/es/vc-upload/interface';

import { reactive } from 'vue';

import { useVbenModal } from '@vben/common-ui';

import { Button, message, Space, Upload } from 'ant-design-vue';

const props = defineProps({
  title: { type: String, default: '导入' },
  downloadTemplateApi: { type: Function, default: null },
  uploadApi: { type: Function, required: true },
  icon: { type: [String], default: '' },
  multiple: { type: Boolean, default: false },
  maxCount: { type: Number, default: 1 },
  autoClose: { type: Boolean, default: true },
});
const emit = defineEmits(['importSuccess', 'uploadSuccess']);
const loading = reactive({
  download: false,
});
const [Modal, modalApi] = useVbenModal({
  fullscreenButton: false,
  showConfirmButton: false,
  cancelText: '完成',
});
const openDialog = () => {
  modalApi.open();
};
const downloadTemplate = async () => {
  loading.download = true;
  try {
    await props.downloadTemplateApi();
  } finally {
    loading.download = false;
  }
};
const handleUploadChange = (info: UploadChangeParam) => {
  if (info.file.status === 'done') {
    emit('uploadSuccess', info.file.response);
    // 检查是否所有文件都已完成上传
    const allFilesDone = info.fileList.every((file) => file.status === 'done');
    if (allFilesDone) {
      if (props.autoClose) {
        modalApi.close();
      }
      message.success('导入成功');
      emit('importSuccess', info.fileList);
    }
  }
};
const uploadFile = ({
  data = {},
  file,
  filename = 'file',
  headers,
  onError,
  onProgress,
  onSuccess,
  withCredentials,
}: UploadRequestOption) => {
  const formData = { ...data };
  formData[filename] = file;
  // 使用框架上传方法已实现 FormData 不需要重复转换
  // const formData = new FormData();
  // if (data) {
  //   Object.keys(data).forEach((key) => {
  //     formData.append(key, data[key] as string);
  //   });
  // }
  // formData.append(filename, file);
  props
    .uploadApi(formData, {
      withCredentials,
      headers,
      onUploadProgress: ({ total, loaded }: { loaded: number; total: number }) => {
        if (onProgress) {
          onProgress({ percent: Number(Math.round((loaded / total) * 100).toFixed(2)) });
        }
      },
    })
    .then((response: unknown) => {
      if (onSuccess) {
        onSuccess(response);
      }
    })
    .catch(onError);
  return {
    abort() {
      console.warn('upload progress is aborted.');
    },
  };
};
</script>

<template>
  <div>
    <Button type="primary" :icon="icon" @click="openDialog">导入</Button>
    <Modal :title="props.title" centered>
      <Space direction="vertical" size="middle">
        <Button v-if="props.downloadTemplateApi" @click="downloadTemplate">下载模板</Button>
        <Upload
          :custom-request="uploadFile"
          :multiple="multiple"
          :max-count="maxCount"
          name="file"
          @change="handleUploadChange"
        >
          <Button type="primary">上传文件</Button>
        </Upload>
      </Space>
    </Modal>
  </div>
</template>

<style></style>
